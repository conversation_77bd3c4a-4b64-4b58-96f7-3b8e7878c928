import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'unified_odoo_service.dart';
import 'local_storage_service.dart';

// نموذج للعمليات المؤجلة
class PendingOperation {
  final String id;
  final String type; // 'update', 'create', 'delete'
  final int? ticketId;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final int retryCount;

  const PendingOperation({
    required this.id,
    required this.type,
    this.ticketId,
    required this.data,
    required this.timestamp,
    this.retryCount = 0,
  });

  PendingOperation copyWith({
    String? id,
    String? type,
    int? ticketId,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    int? retryCount,
  }) {
    return PendingOperation(
      id: id ?? this.id,
      type: type ?? this.type,
      ticketId: ticketId ?? this.ticketId,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      retryCount: retryCount ?? this.retryCount,
    );
  }
}

class SyncService {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final UnifiedOdooService _odooService = UnifiedOdooService();
  final Connectivity _connectivity = Connectivity();

  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  Timer? _syncTimer;

  final List<PendingOperation> _pendingOperations = [];
  bool _isSyncing = false;

  // Stream controllers للإشعارات
  final StreamController<bool> _syncStatusController =
      StreamController<bool>.broadcast();
  final StreamController<String> _syncMessageController =
      StreamController<String>.broadcast();

  // Getters للـ streams
  Stream<bool> get syncStatusStream => _syncStatusController.stream;
  Stream<String> get syncMessageStream => _syncMessageController.stream;

  // تهيئة الخدمة
  Future<void> init() async {
    // مراقبة تغييرات الاتصال
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _onConnectivityChanged,
    );

    // تحميل العمليات المؤجلة من التخزين المحلي
    await _loadPendingOperations();

    // بدء المزامنة الدورية
    _startPeriodicSync();

    // محاولة المزامنة الأولية
    await _checkAndSync();
  }

  // تنظيف الموارد
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
    _syncStatusController.close();
    _syncMessageController.close();
  }

  // مراقبة تغييرات الاتصال
  void _onConnectivityChanged(ConnectivityResult result) {
    final hasConnection =
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.ethernet;

    if (hasConnection && !_isSyncing) {
      _checkAndSync();
    }
  }

  // بدء المزامنة الدورية
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _checkAndSync();
    });
  }

  // فحص الاتصال والمزامنة
  Future<void> _checkAndSync() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    final hasConnection =
        connectivityResult == ConnectivityResult.wifi ||
        connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.ethernet;

    if (hasConnection && !_isSyncing) {
      await syncData();
    }
  }

  // المزامنة الرئيسية
  Future<void> syncData() async {
    if (_isSyncing) return;

    _isSyncing = true;
    _syncStatusController.add(true);
    _syncMessageController.add('جاري المزامنة...');

    try {
      // 1. مزامنة العمليات المؤجلة
      await _syncPendingOperations();

      // 2. تحديث التذاكر من الخادم
      await _syncTicketsFromServer();

      // 3. تحديث بيانات المستخدم
      await _syncUserData();

      _syncMessageController.add('تمت المزامنة بنجاح');
    } catch (e) {
      _syncMessageController.add('خطأ في المزامنة: ${e.toString()}');
    } finally {
      _isSyncing = false;
      _syncStatusController.add(false);
    }
  }

  // مزامنة العمليات المؤجلة
  Future<void> _syncPendingOperations() async {
    final operationsToRemove = <PendingOperation>[];

    for (final operation in List.from(_pendingOperations)) {
      try {
        bool success = false;

        switch (operation.type) {
          case 'update':
            if (operation.ticketId != null) {
              success = await _odooService.updateTicket(
                operation.ticketId!,
                operation.data,
              );
            }
            break;
          // يمكن إضافة المزيد من أنواع العمليات هنا
        }

        if (success) {
          operationsToRemove.add(operation);
        } else {
          // زيادة عداد المحاولات
          final updatedOperation = operation.copyWith(
            retryCount: operation.retryCount + 1,
          );
          final index = _pendingOperations.indexOf(operation);
          if (index != -1) {
            _pendingOperations[index] = updatedOperation;
          }

          // إزالة العمليات التي فشلت أكثر من 3 مرات
          if (updatedOperation.retryCount > 3) {
            operationsToRemove.add(operation);
          }
        }
      } catch (e) {
        // في حالة الخطأ، نزيد عداد المحاولات
        final updatedOperation = operation.copyWith(
          retryCount: operation.retryCount + 1,
        );
        final index = _pendingOperations.indexOf(operation);
        if (index != -1) {
          _pendingOperations[index] = updatedOperation;
        }

        if (updatedOperation.retryCount > 3) {
          operationsToRemove.add(operation);
        }
      }
    }

    // إزالة العمليات المكتملة أو الفاشلة
    for (final operation in operationsToRemove) {
      _pendingOperations.remove(operation);
    }

    // حفظ العمليات المؤجلة المحدثة
    await _savePendingOperations();
  }

  // مزامنة التذاكر من الخادم
  Future<void> _syncTicketsFromServer() async {
    try {
      final currentUser = LocalStorageService.getUser();
      if (currentUser == null) return;

      final tickets = await _odooService.getTickets();
      await LocalStorageService.saveTickets(tickets);
      await LocalStorageService.setLastSyncTime(DateTime.now());
    } catch (e) {
      // في حالة الخطأ، نحتفظ بالبيانات المحلية
      rethrow;
    }
  }

  // مزامنة بيانات المستخدم
  Future<void> _syncUserData() async {
    try {
      final userInfo = await _odooService.getUserInfo();
      if (userInfo != null) {
        await LocalStorageService.saveUser(userInfo);
      }
    } catch (e) {
      // في حالة الخطأ، نحتفظ بالبيانات المحلية
    }
  }

  // إضافة عملية مؤجلة
  Future<void> addPendingOperation(PendingOperation operation) async {
    _pendingOperations.add(operation);
    await _savePendingOperations();

    // محاولة المزامنة فوراً إذا كان هناك اتصال
    _checkAndSync();
  }

  // إضافة عملية تحديث تذكرة مؤجلة
  Future<void> addPendingTicketUpdate(
    int ticketId,
    Map<String, dynamic> data,
  ) async {
    final operation = PendingOperation(
      id: '${DateTime.now().millisecondsSinceEpoch}_update_$ticketId',
      type: 'update',
      ticketId: ticketId,
      data: data,
      timestamp: DateTime.now(),
    );

    await addPendingOperation(operation);
  }

  // تحميل العمليات المؤجلة من التخزين المحلي
  Future<void> _loadPendingOperations() async {
    // يمكن تطوير هذا لحفظ العمليات في Hive أو SharedPreferences
    // للبساطة، سنتركها فارغة الآن
  }

  // حفظ العمليات المؤجلة في التخزين المحلي
  Future<void> _savePendingOperations() async {
    // يمكن تطوير هذا لحفظ العمليات في Hive أو SharedPreferences
    // للبساطة، سنتركها فارغة الآن
  }

  // الحصول على حالة المزامنة
  bool get isSyncing => _isSyncing;

  // الحصول على عدد العمليات المؤجلة
  int get pendingOperationsCount => _pendingOperations.length;

  // مزامنة يدوية
  Future<void> forcSync() async {
    await syncData();
  }
}
