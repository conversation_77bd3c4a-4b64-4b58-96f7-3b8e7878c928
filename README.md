# mrady_odoo

A new Flutter project.

## Getting Started

# تطبيق مرادي أودو - نظام إدارة التذاكر

تطبيق Flutter متكامل مرتبط مع Odoo عبر API لإدارة تذاكر الدعم الفني.

## 🔑 المميزات

### صفحة تسجيل الدخول
- حقل موحد لإدخال البريد الإلكتروني أو رقم الهاتف
- معالجة تلقائية لأرقام الهواتف (إضافة @mrady.com)
- خيار "تذكرني" للدخول التلقائي
- حفظ بيانات الجلسة محلياً

### إدارة التذاكر
- عرض قائمة التذاكر مع الفلترة والبحث
- تفاصيل كاملة لكل تذكرة
- تعديل التذاكر حسب الصلاحيات
- إحصائيات التذاكر (المجموع، الحالات، الأولويات)

### العمل أوفلاين (Offline-First)
- تخزين التذاكر محلياً باستخدام Hive
- مزامنة تلقائية عند توفر الاتصال
- حفظ التعديلات محلياً وإرسالها لاحقاً
- مراقبة حالة الاتصال

### إدارة الصلاحيات
- التحقق من صلاحيات المستخدم من Odoo
- إخفاء/إظهار خيارات التعديل حسب الصلاحيات
- دعم مجموعات المستخدمين المختلفة

## 🔧 التقنيات المستخدمة

- **Flutter 3+** - إطار العمل الأساسي
- **Riverpod** - إدارة الحالة
- **Dio** - طلبات HTTP
- **Hive** - قاعدة بيانات محلية
- **SharedPreferences** - تخزين الإعدادات
- **Connectivity Plus** - مراقبة الاتصال
- **Material 3** - تصميم حديث

## 🚀 التشغيل

### المتطلبات
- Flutter SDK 3.7.2 أو أحدث
- Dart SDK
- Android Studio أو VS Code

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd mrady_odoo
```

2. **تثبيت المكتبات**
```bash
flutter pub get
```

3. **إنشاء الملفات المولدة**
```bash
flutter packages pub run build_runner build
```

4. **تشغيل التطبيق**
```bash
flutter run
```

## 📱 كيفية الاستخدام

### تسجيل الدخول
1. أدخل البريد الإلكتروني أو رقم الهاتف
2. أدخل كلمة المرور
3. اختر "تذكرني" للدخول التلقائي
4. اضغط "تسجيل الدخول"

### إدارة التذاكر
1. عرض قائمة التذاكر في الشاشة الرئيسية
2. استخدم البحث للعثور على تذاكر محددة
3. اضغط على أيقونة الفلترة لتصفية التذاكر
4. اضغط على أي تذكرة لعرض التفاصيل
5. استخدم زر التعديل لتحديث التذكرة (إذا كانت لديك الصلاحيات)

### العمل أوفلاين
- التطبيق يعمل تلقائياً في وضع أوفلاين
- التعديلات تُحفظ محلياً وتُرسل عند توفر الاتصال
- مؤشر الاتصال يظهر في أعلى الشاشة

## ⚙️ الإعدادات

### خادم Odoo
- الرابط: `https://mrady.odoo.com`
- قاعدة البيانات: `mrady`

### تخصيص الإعدادات
يمكن تعديل إعدادات الخادم في ملف `lib/services/odoo_service.dart`:

```dart
static const String baseUrl = 'https://mrady.odoo.com';
static const String database = 'mrady';
```

## 🏗️ بنية المشروع

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── user.dart
│   └── ticket.dart
├── services/                 # الخدمات
│   ├── odoo_service.dart
│   ├── local_storage_service.dart
│   └── sync_service.dart
├── providers/                # مزودي الحالة
│   ├── auth_provider.dart
│   ├── tickets_provider.dart
│   └── connectivity_provider.dart
├── screens/                  # الشاشات
│   ├── login_screen.dart
│   ├── tickets_list_screen.dart
│   └── ticket_detail_screen.dart
└── widgets/                  # المكونات المساعدة
    ├── ticket_card.dart
    └── tickets_filter_sheet.dart
```

## 🔒 الأمان

- تشفير بيانات الجلسة
- التحقق من الصلاحيات قبل كل عملية
- عدم تخزين كلمات المرور محلياً
- استخدام HTTPS لجميع الطلبات

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال**
   - تأكد من الاتصال بالإنترنت
   - تحقق من صحة رابط الخادم

2. **فشل تسجيل الدخول**
   - تأكد من صحة البيانات
   - تحقق من حالة الخادم

3. **مشاكل المزامنة**
   - أعد تشغيل التطبيق
   - امسح البيانات المحلية من الإعدادات

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
