// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_attachment.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TicketAttachmentAdapter extends TypeAdapter<TicketAttachment> {
  @override
  final int typeId = 4;

  @override
  TicketAttachment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TicketAttachment(
      id: fields[0] as int,
      name: fields[1] as String,
      mimetype: fields[2] as String,
      fileSize: fields[3] as int,
      url: fields[4] as String?,
      resId: fields[5] as int,
      resModel: fields[6] as String,
      createDate: fields[7] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, TicketAttachment obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.mimetype)
      ..writeByte(3)
      ..write(obj.fileSize)
      ..writeByte(4)
      ..write(obj.url)
      ..writeByte(5)
      ..write(obj.resId)
      ..writeByte(6)
      ..write(obj.resModel)
      ..writeByte(7)
      ..write(obj.createDate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TicketAttachmentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketAttachment _$TicketAttachmentFromJson(Map<String, dynamic> json) =>
    TicketAttachment(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      mimetype: json['mimetype'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      url: json['url'] as String?,
      resId: (json['resId'] as num).toInt(),
      resModel: json['resModel'] as String,
      createDate: DateTime.parse(json['createDate'] as String),
    );

Map<String, dynamic> _$TicketAttachmentToJson(TicketAttachment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'mimetype': instance.mimetype,
      'fileSize': instance.fileSize,
      'url': instance.url,
      'resId': instance.resId,
      'resModel': instance.resModel,
      'createDate': instance.createDate.toIso8601String(),
    };
