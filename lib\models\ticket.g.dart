// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TicketAdapter extends TypeAdapter<Ticket> {
  @override
  final int typeId = 1;

  @override
  Ticket read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Ticket(
      id: fields[0] as int,
      name: fields[1] as String,
      description: fields[2] as String?,
      stage: fields[3] as String,
      priority: fields[4] as String,
      partnerId: fields[5] as int?,
      partnerName: fields[6] as String?,
      userId: fields[7] as int?,
      userName: fields[8] as String?,
      createDate: fields[9] as DateTime,
      writeDate: fields[10] as DateTime,
      teamId: fields[11] as int?,
      teamName: fields[12] as String?,
      active: fields[31] as bool,
      email: fields[32] as String?,
      phone: fields[33] as String?,
      mobile: fields[13] as String?,
      street: fields[14] as String?,
      street2: fields[15] as String?,
      city: fields[16] as String?,
      state: fields[17] as String?,
      country: fields[18] as String?,
      zip: fields[19] as String?,
      companyName: fields[20] as String?,
      category: fields[21] as String?,
      tags: (fields[22] as List?)?.cast<String>(),
      kanbanState: fields[23] as String?,
      color: fields[24] as int?,
      dateDeadline: fields[25] as DateTime?,
      dateLastStageUpdate: fields[26] as DateTime?,
      closeDate: fields[27] as DateTime?,
      ratingLastValue: fields[28] as double?,
      slaDeadline: fields[29] as DateTime?,
      slaReachedDatetime: fields[30] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, Ticket obj) {
    writer
      ..writeByte(34)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.stage)
      ..writeByte(4)
      ..write(obj.priority)
      ..writeByte(5)
      ..write(obj.partnerId)
      ..writeByte(6)
      ..write(obj.partnerName)
      ..writeByte(7)
      ..write(obj.userId)
      ..writeByte(8)
      ..write(obj.userName)
      ..writeByte(9)
      ..write(obj.createDate)
      ..writeByte(10)
      ..write(obj.writeDate)
      ..writeByte(11)
      ..write(obj.teamId)
      ..writeByte(12)
      ..write(obj.teamName)
      ..writeByte(13)
      ..write(obj.mobile)
      ..writeByte(14)
      ..write(obj.street)
      ..writeByte(15)
      ..write(obj.street2)
      ..writeByte(16)
      ..write(obj.city)
      ..writeByte(17)
      ..write(obj.state)
      ..writeByte(18)
      ..write(obj.country)
      ..writeByte(19)
      ..write(obj.zip)
      ..writeByte(20)
      ..write(obj.companyName)
      ..writeByte(21)
      ..write(obj.category)
      ..writeByte(22)
      ..write(obj.tags)
      ..writeByte(23)
      ..write(obj.kanbanState)
      ..writeByte(24)
      ..write(obj.color)
      ..writeByte(25)
      ..write(obj.dateDeadline)
      ..writeByte(26)
      ..write(obj.dateLastStageUpdate)
      ..writeByte(27)
      ..write(obj.closeDate)
      ..writeByte(28)
      ..write(obj.ratingLastValue)
      ..writeByte(29)
      ..write(obj.slaDeadline)
      ..writeByte(30)
      ..write(obj.slaReachedDatetime)
      ..writeByte(31)
      ..write(obj.active)
      ..writeByte(32)
      ..write(obj.email)
      ..writeByte(33)
      ..write(obj.phone);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TicketAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Ticket _$TicketFromJson(Map<String, dynamic> json) => Ticket(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      stage: json['stage'] as String,
      priority: json['priority'] as String,
      partnerId: (json['partnerId'] as num?)?.toInt(),
      partnerName: json['partnerName'] as String?,
      userId: (json['userId'] as num?)?.toInt(),
      userName: json['userName'] as String?,
      createDate: DateTime.parse(json['createDate'] as String),
      writeDate: DateTime.parse(json['writeDate'] as String),
      teamId: (json['teamId'] as num?)?.toInt(),
      teamName: json['teamName'] as String?,
      active: json['active'] as bool,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      mobile: json['mobile'] as String?,
      street: json['street'] as String?,
      street2: json['street2'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      zip: json['zip'] as String?,
      companyName: json['companyName'] as String?,
      category: json['category'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      kanbanState: json['kanbanState'] as String?,
      color: (json['color'] as num?)?.toInt(),
      dateDeadline: json['dateDeadline'] == null
          ? null
          : DateTime.parse(json['dateDeadline'] as String),
      dateLastStageUpdate: json['dateLastStageUpdate'] == null
          ? null
          : DateTime.parse(json['dateLastStageUpdate'] as String),
      closeDate: json['closeDate'] == null
          ? null
          : DateTime.parse(json['closeDate'] as String),
      ratingLastValue: (json['ratingLastValue'] as num?)?.toDouble(),
      slaDeadline: json['slaDeadline'] == null
          ? null
          : DateTime.parse(json['slaDeadline'] as String),
      slaReachedDatetime: json['slaReachedDatetime'] == null
          ? null
          : DateTime.parse(json['slaReachedDatetime'] as String),
    );

Map<String, dynamic> _$TicketToJson(Ticket instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'stage': instance.stage,
      'priority': instance.priority,
      'partnerId': instance.partnerId,
      'partnerName': instance.partnerName,
      'userId': instance.userId,
      'userName': instance.userName,
      'createDate': instance.createDate.toIso8601String(),
      'writeDate': instance.writeDate.toIso8601String(),
      'teamId': instance.teamId,
      'teamName': instance.teamName,
      'mobile': instance.mobile,
      'street': instance.street,
      'street2': instance.street2,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'zip': instance.zip,
      'companyName': instance.companyName,
      'category': instance.category,
      'tags': instance.tags,
      'kanbanState': instance.kanbanState,
      'color': instance.color,
      'dateDeadline': instance.dateDeadline?.toIso8601String(),
      'dateLastStageUpdate': instance.dateLastStageUpdate?.toIso8601String(),
      'closeDate': instance.closeDate?.toIso8601String(),
      'ratingLastValue': instance.ratingLastValue,
      'slaDeadline': instance.slaDeadline?.toIso8601String(),
      'slaReachedDatetime': instance.slaReachedDatetime?.toIso8601String(),
      'active': instance.active,
      'email': instance.email,
      'phone': instance.phone,
    };
