import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/ticket.dart';

class OdooService {
  static const String baseUrl = 'https://mrady.odoo.com';
  static const String database = 'mrady';

  // مفتاح للتبديل بين الخدمة الحقيقية والوهمية للتطوير
  static const bool useMockService =
      true; // غير هذا إلى false لاستخدام الخدمة الحقيقية

  late final Dio _dio;
  String? _sessionId;
  int? _userId;

  OdooService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {'Content-Type': 'application/json'},
      ),
    );

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          if (_sessionId != null) {
            options.headers['Cookie'] = 'session_id=$_sessionId';
          }
          handler.next(options);
        },
        onError: (error, handler) {
          print('Odoo API Error: ${error.message}');
          handler.next(error);
        },
      ),
    );
  }

  Future<void> _loadSession() async {
    final prefs = await SharedPreferences.getInstance();
    _sessionId = prefs.getString('session_id');
    _userId = prefs.getInt('user_id');
  }

  Future<void> _saveSession(String sessionId, int userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('session_id', sessionId);
    await prefs.setInt('user_id', userId);
    _sessionId = sessionId;
    _userId = userId;
  }

  Future<void> _clearSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('session_id');
    await prefs.remove('user_id');
    _sessionId = null;
    _userId = null;
  }

  String _processEmailOrPhone(String input) {
    // إذا كان الإدخال يحتوي على @ فهو بريد إلكتروني
    if (input.contains('@')) {
      return input;
    }
    // إذا لم يحتوي على @ فهو رقم هاتف، نضيف له @mrady.com
    return '$<EMAIL>';
  }

  Future<User?> login(String emailOrPhone, String password) async {
    try {
      final processedEmail = _processEmailOrPhone(emailOrPhone);

      final response = await _dio.post(
        '/web/session/authenticate',
        data: {
          'jsonrpc': '2.0',
          'method': 'call',
          'params': {
            'db': database,
            'login': processedEmail,
            'password': password,
          },
        },
      );

      // التحقق من وجود خطأ في الاستجابة
      if (response.data['error'] != null) {
        final error = response.data['error'];
        throw Exception(
          'خطأ من الخادم: ${error['message'] ?? 'خطأ غير معروف'}',
        );
      }

      final result = response.data['result'];
      if (result != null && result['uid'] != false) {
        final sessionId =
            response.headers['set-cookie']
                ?.firstWhere((cookie) => cookie.startsWith('session_id='))
                .split(';')[0]
                .split('=')[1];

        if (sessionId != null) {
          await _saveSession(sessionId, result['uid']);

          // جلب بيانات المستخدم
          final userInfo = await getUserInfo();
          return userInfo;
        }
      }

      // إذا وصلنا هنا فإن بيانات تسجيل الدخول غير صحيحة
      throw Exception(
        'بيانات تسجيل الدخول غير صحيحة. تأكد من البريد الإلكتروني وكلمة المرور',
      );
    } on DioException catch (e) {
      print('Login error: $e');
      if (e.type == DioExceptionType.connectionError) {
        throw Exception('خطأ في الاتصال بالخادم. تأكد من الاتصال بالإنترنت');
      } else if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception('انتهت مهلة الاتصال. حاول مرة أخرى');
      } else if (e.response?.statusCode == 404) {
        throw Exception('الخادم غير متاح حالياً');
      } else if (e.response?.statusCode == 500) {
        throw Exception('خطأ في الخادم الداخلي');
      } else {
        throw Exception('خطأ في الشبكة: ${e.message}');
      }
    } catch (e) {
      print('Login error: $e');
      throw Exception('خطأ غير متوقع: $e');
    }
  }

  Future<User?> getUserInfo() async {
    try {
      await _loadSession();
      if (_sessionId == null || _userId == null) return null;

      final response = await _dio.post(
        '/web/dataset/call_kw',
        data: {
          'jsonrpc': '2.0',
          'method': 'call',
          'params': {
            'model': 'res.users',
            'method': 'read',
            'args': [_userId],
            'kwargs': {
              'fields': ['name', 'email', 'phone', 'groups_id'],
            },
          },
        },
      );

      final result = response.data['result'];
      if (result != null && result.isNotEmpty) {
        final userData = result[0];

        // جلب أسماء المجموعات
        final groupIds = List<int>.from(userData['groups_id'] ?? []);
        final groups = await _getGroupNames(groupIds);

        return User(
          id: userData['id'],
          name: userData['name'] ?? '',
          email: userData['email'] ?? '',
          phone: userData['phone'],
          groups: groups,
          sessionId: _sessionId,
          lastLogin: DateTime.now(),
        );
      }
      return null;
    } catch (e) {
      print('Get user info error: $e');
      return null;
    }
  }

  Future<List<String>> _getGroupNames(List<int> groupIds) async {
    try {
      if (groupIds.isEmpty) return [];

      final response = await _dio.post(
        '/web/dataset/call_kw',
        data: {
          'jsonrpc': '2.0',
          'method': 'call',
          'params': {
            'model': 'res.groups',
            'method': 'read',
            'args': [groupIds],
            'kwargs': {
              'fields': ['name'],
            },
          },
        },
      );

      final result = response.data['result'];
      if (result != null) {
        return List<String>.from(result.map((group) => group['name'] ?? ''));
      }
      return [];
    } catch (e) {
      print('Get group names error: $e');
      return [];
    }
  }

  Future<List<Ticket>> getTickets({int? userId}) async {
    try {
      await _loadSession();
      if (_sessionId == null) return [];

      final domain =
          userId != null
              ? [
                ['user_id', '=', userId],
              ]
              : [];

      final response = await _dio.post(
        '/web/dataset/call_kw',
        data: {
          'jsonrpc': '2.0',
          'method': 'call',
          'params': {
            'model': 'helpdesk.ticket',
            'method': 'search_read',
            'args': [domain],
            'kwargs': {
              'fields': [
                'name',
                'description',
                'stage_id',
                'priority',
                'partner_id',
                'user_id',
                'create_date',
                'write_date',
                'team_id',
                'active',
                'email_from',
                'phone',
              ],
              'order': 'create_date desc',
            },
          },
        },
      );

      final result = response.data['result'];
      if (result != null) {
        return List<Ticket>.from(
          result.map(
            (ticketData) => Ticket(
              id: ticketData['id'],
              name: ticketData['name'] ?? '',
              description: ticketData['description'],
              stage: ticketData['stage_id']?[1] ?? 'new',
              priority: ticketData['priority']?.toString() ?? '1',
              partnerId: ticketData['partner_id']?[0],
              partnerName: ticketData['partner_id']?[1],
              userId: ticketData['user_id']?[0],
              userName: ticketData['user_id']?[1],
              createDate: DateTime.parse(ticketData['create_date']),
              writeDate: DateTime.parse(ticketData['write_date']),
              teamId: ticketData['team_id']?[0],
              teamName: ticketData['team_id']?[1],
              active: ticketData['active'] ?? true,
              email: ticketData['email_from'],
              phone: ticketData['phone'],
            ),
          ),
        );
      }
      return [];
    } catch (e) {
      print('Get tickets error: $e');
      return [];
    }
  }

  Future<bool> updateTicket(int ticketId, Map<String, dynamic> values) async {
    try {
      await _loadSession();
      if (_sessionId == null) return false;

      final response = await _dio.post(
        '/web/dataset/call_kw',
        data: {
          'jsonrpc': '2.0',
          'method': 'call',
          'params': {
            'model': 'helpdesk.ticket',
            'method': 'write',
            'args': [ticketId, values],
            'kwargs': {},
          },
        },
      );

      return response.data['result'] == true;
    } catch (e) {
      print('Update ticket error: $e');
      return false;
    }
  }

  Future<void> logout() async {
    try {
      await _dio.post('/web/session/destroy');
    } catch (e) {
      print('Logout error: $e');
    } finally {
      await _clearSession();
    }
  }

  bool get isLoggedIn => _sessionId != null;
  int? get currentUserId => _userId;
}
