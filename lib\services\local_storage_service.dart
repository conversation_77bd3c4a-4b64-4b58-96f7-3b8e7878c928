import 'package:hive_flutter/hive_flutter.dart';
import '../models/user.dart';
import '../models/ticket.dart';
import '../models/ticket_message.dart';
import '../models/ticket_attachment.dart';

class LocalStorageService {
  static const String _userBoxName = 'user_box';
  static const String _ticketsBoxName = 'tickets_box';
  static const String _userKey = 'current_user';

  static Box<User>? _userBox;
  static Box<Ticket>? _ticketsBox;

  static Future<void> init() async {
    await Hive.initFlutter();

    // تسجيل المحولات
    Hive.registerAdapter(UserAdapter());
    Hive.registerAdapter(TicketAdapter());
    Hive.registerAdapter(TicketMessageAdapter());
    Hive.registerAdapter(TicketAttachmentAdapter());

    // فتح الصناديق
    _userBox = await Hive.openBox<User>(_userBoxName);
    _ticketsBox = await Hive.openBox<Ticket>(_ticketsBoxName);
  }

  // إدارة المستخدم
  static Future<void> saveUser(User user) async {
    await _userBox?.put(_userKey, user);
  }

  static User? getUser() {
    return _userBox?.get(_userKey);
  }

  static Future<void> clearUser() async {
    await _userBox?.delete(_userKey);
  }

  // إدارة التذاكر
  static Future<void> saveTickets(List<Ticket> tickets) async {
    if (_ticketsBox == null) return;

    // مسح التذاكر القديمة
    await _ticketsBox!.clear();

    // حفظ التذاكر الجديدة
    final ticketsMap = <int, Ticket>{};
    for (final ticket in tickets) {
      ticketsMap[ticket.id] = ticket;
    }
    await _ticketsBox!.putAll(ticketsMap);
  }

  static List<Ticket> getTickets() {
    if (_ticketsBox == null) return [];
    return _ticketsBox!.values.toList();
  }

  static Future<void> saveTicket(Ticket ticket) async {
    await _ticketsBox?.put(ticket.id, ticket);
  }

  static Ticket? getTicket(int id) {
    return _ticketsBox?.get(id);
  }

  static Future<void> deleteTicket(int id) async {
    await _ticketsBox?.delete(id);
  }

  static Future<void> clearTickets() async {
    await _ticketsBox?.clear();
  }

  // إدارة الإعدادات
  static Future<void> setRememberMe(bool remember) async {
    final box = await Hive.openBox('settings');
    await box.put('remember_me', remember);
  }

  static Future<bool> getRememberMe() async {
    final box = await Hive.openBox('settings');
    return box.get('remember_me', defaultValue: false);
  }

  static Future<void> setLastSyncTime(DateTime time) async {
    final box = await Hive.openBox('settings');
    await box.put('last_sync_time', time.millisecondsSinceEpoch);
  }

  static Future<DateTime?> getLastSyncTime() async {
    final box = await Hive.openBox('settings');
    final timestamp = box.get('last_sync_time');
    if (timestamp != null) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    }
    return null;
  }

  // تنظيف جميع البيانات
  static Future<void> clearAll() async {
    await _userBox?.clear();
    await _ticketsBox?.clear();
    final settingsBox = await Hive.openBox('settings');
    await settingsBox.clear();
  }

  // إحصائيات
  static int get ticketsCount => _ticketsBox?.length ?? 0;
  static bool get hasUser => _userBox?.containsKey(_userKey) ?? false;
  static bool get hasTickets => (_ticketsBox?.length ?? 0) > 0;

  // البحث في التذاكر
  static List<Ticket> searchTickets(String query) {
    if (_ticketsBox == null || query.isEmpty) return getTickets();

    final allTickets = getTickets();
    return allTickets.where((ticket) {
      return ticket.name.toLowerCase().contains(query.toLowerCase()) ||
          (ticket.description?.toLowerCase().contains(query.toLowerCase()) ??
              false) ||
          ticket.partnerName?.toLowerCase().contains(query.toLowerCase()) ==
              true;
    }).toList();
  }

  // فلترة التذاكر حسب الحالة
  static List<Ticket> getTicketsByStage(String stage) {
    if (_ticketsBox == null) return [];

    final allTickets = getTickets();
    return allTickets.where((ticket) => ticket.stage == stage).toList();
  }

  // فلترة التذاكر حسب الأولوية
  static List<Ticket> getTicketsByPriority(String priority) {
    if (_ticketsBox == null) return [];

    final allTickets = getTickets();
    return allTickets.where((ticket) => ticket.priority == priority).toList();
  }

  // فلترة التذاكر حسب المستخدم
  static List<Ticket> getTicketsByUser(int userId) {
    if (_ticketsBox == null) return [];

    final allTickets = getTickets();
    return allTickets.where((ticket) => ticket.userId == userId).toList();
  }
}
