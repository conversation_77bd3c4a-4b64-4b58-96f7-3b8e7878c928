import '../models/user.dart';
import '../models/ticket.dart';
import '../models/ticket_message.dart';
import '../models/ticket_attachment.dart';
import 'real_odoo_service.dart';
import 'mock_odoo_service.dart';

/// خدمة موحدة تتبديل بين الخدمة الحقيقية والوهمية
class UnifiedOdooService {
  static const bool _useMockService = false; // غير هذا إلى true للخدمة الوهمية

  late final dynamic _service;

  UnifiedOdooService() {
    if (_useMockService) {
      _service = MockOdooService();
    } else {
      _service = RealOdooService();
    }
  }

  // تسجيل الدخول
  Future<User?> login(String emailOrPhone, String password) async {
    return await _service.login(emailOrPhone, password);
  }

  // جلب معلومات المستخدم
  Future<User?> getUserInfo() async {
    return await _service.getUserInfo();
  }

  // جلب أسماء المجموعات
  Future<List<String>> getGroupNames(List<int> groupIds) async {
    return await _service.getGroupNames(groupIds);
  }

  // جلب التذاكر
  Future<List<Ticket>> getTickets({String? searchQuery, int limit = 80}) async {
    return await _service.getTickets(searchQuery: searchQuery, limit: limit);
  }

  // تحديث تذكرة
  Future<bool> updateTicket(int ticketId, Map<String, dynamic> values) async {
    return await _service.updateTicket(ticketId, values);
  }

  // جلب رسائل التذكرة
  Future<List<TicketMessage>> getTicketMessages(int ticketId) async {
    if (_useMockService) {
      // إرجاع قائمة فارغة للخدمة الوهمية
      return [];
    }
    return await _service.getTicketMessages(ticketId);
  }

  // جلب مرفقات التذكرة
  Future<List<TicketAttachment>> getTicketAttachments(int ticketId) async {
    if (_useMockService) {
      // إرجاع قائمة فارغة للخدمة الوهمية
      return [];
    }
    return await _service.getTicketAttachments(ticketId);
  }

  // تحميل المرفق
  Future<String?> downloadAttachment(int attachmentId) async {
    if (_useMockService) {
      return null;
    }
    return await _service.downloadAttachment(attachmentId);
  }

  // تسجيل الخروج
  Future<void> logout() async {
    return await _service.logout();
  }

  // التحقق من صحة الجلسة
  Future<bool> validateSession() async {
    if (_service is MockOdooService) {
      return await _service.validateSession();
    } else {
      // للخدمة الحقيقية، يمكن إضافة منطق التحقق هنا
      return true;
    }
  }

  // معرفة ما إذا كنا نستخدم الخدمة الوهمية
  bool get isUsingMockService => _useMockService;
}
