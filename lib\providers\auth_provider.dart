import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user.dart';
import '../services/unified_odoo_service.dart';
import '../services/local_storage_service.dart';
import 'tickets_provider.dart';

// حالة المصادقة
enum AuthState { initial, loading, authenticated, unauthenticated, error }

// فئة حالة المصادقة
class AuthStateData {
  final AuthState state;
  final User? user;
  final String? errorMessage;

  const AuthStateData({required this.state, this.user, this.errorMessage});

  AuthStateData copyWith({
    AuthState? state,
    User? user,
    String? errorMessage,
    bool clearError = false,
  }) {
    return AuthStateData(
      state: state ?? this.state,
      user: user ?? this.user,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
    );
  }
}

// مزود خدمة Odoo
final odooServiceProvider = Provider<UnifiedOdooService>((ref) {
  return UnifiedOdooService();
});

// مزود حالة المصادقة
class AuthNotifier extends StateNotifier<AuthStateData> {
  final UnifiedOdooService _odooService;
  final Ref _ref;

  AuthNotifier(this._odooService, this._ref)
    : super(const AuthStateData(state: AuthState.initial)) {
    _checkAuthStatus();
  }

  // فحص حالة المصادقة عند بدء التطبيق
  Future<void> _checkAuthStatus() async {
    final savedUser = LocalStorageService.getUser();
    final rememberMe = await LocalStorageService.getRememberMe();

    if (savedUser != null && rememberMe) {
      // التحقق من صحة الجلسة
      final userInfo = await _odooService.getUserInfo();
      if (userInfo != null) {
        state = AuthStateData(state: AuthState.authenticated, user: userInfo);

        // تحديث التذاكر عند التحقق من الجلسة
        _ref.read(ticketsProvider.notifier).refreshTickets();
      } else {
        // الجلسة منتهية الصلاحية
        await logout();
      }
    } else {
      state = const AuthStateData(state: AuthState.unauthenticated);
    }
  }

  // تسجيل الدخول
  Future<bool> login(
    String emailOrPhone,
    String password,
    bool rememberMe,
  ) async {
    state = state.copyWith(state: AuthState.loading);

    try {
      final user = await _odooService.login(emailOrPhone, password);

      if (user != null) {
        // حفظ المستخدم محلياً
        await LocalStorageService.saveUser(user);
        await LocalStorageService.setRememberMe(rememberMe);

        state = AuthStateData(state: AuthState.authenticated, user: user);

        // تحديث التذاكر بعد تسجيل الدخول
        _ref.read(ticketsProvider.notifier).refreshTickets();

        return true;
      } else {
        state = const AuthStateData(
          state: AuthState.error,
          errorMessage: 'بيانات الدخول غير صحيحة',
        );
        return false;
      }
    } catch (e) {
      // استخراج رسالة الخطأ من Exception
      String errorMessage = 'خطأ غير متوقع';
      if (e is Exception) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      } else {
        errorMessage = e.toString();
      }

      state = AuthStateData(state: AuthState.error, errorMessage: errorMessage);
      return false;
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    state = state.copyWith(state: AuthState.loading);

    try {
      await _odooService.logout();
    } catch (e) {
      // تجاهل أخطاء تسجيل الخروج
    }

    // مسح البيانات المحلية
    await LocalStorageService.clearUser();
    await LocalStorageService.setRememberMe(false);

    state = const AuthStateData(state: AuthState.unauthenticated);
  }

  // تحديث بيانات المستخدم
  Future<void> refreshUser() async {
    if (state.state != AuthState.authenticated) return;

    try {
      final userInfo = await _odooService.getUserInfo();
      if (userInfo != null) {
        await LocalStorageService.saveUser(userInfo);
        state = state.copyWith(user: userInfo);
      }
    } catch (e) {
      // في حالة فشل التحديث، نحتفظ بالبيانات الحالية
    }
  }

  // مسح رسالة الخطأ
  void clearError() {
    if (state.state == AuthState.error) {
      state = state.copyWith(
        state: AuthState.unauthenticated,
        errorMessage: null,
      );
    }
  }
}

// مزود المصادقة
final authProvider = StateNotifierProvider<AuthNotifier, AuthStateData>((ref) {
  final odooService = ref.watch(odooServiceProvider);
  return AuthNotifier(odooService, ref);
});

// مزودات مساعدة
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.state == AuthState.authenticated;
});

final isLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.state == AuthState.loading;
});

final authErrorProvider = Provider<String?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.errorMessage;
});
