import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ticket.g.dart';

@HiveType(typeId: 1)
@JsonSerializable()
class Ticket {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String? description;

  @HiveField(3)
  final String stage;

  @HiveField(4)
  final String priority;

  @HiveField(5)
  final int? partnerId;

  @HiveField(6)
  final String? partnerName;

  @HiveField(7)
  final int? userId;

  @HiveField(8)
  final String? userName;

  @HiveField(9)
  final DateTime createDate;

  @HiveField(10)
  final DateTime writeDate;

  @HiveField(11)
  final int? teamId;

  @HiveField(12)
  final String? teamName;

  // حقول جديدة للتفاصيل المحسنة
  @HiveField(13)
  final String? mobile;

  @HiveField(14)
  final String? street;

  @HiveField(15)
  final String? street2;

  @HiveField(16)
  final String? city;

  @HiveField(17)
  final String? state;

  @HiveField(18)
  final String? country;

  @HiveField(19)
  final String? zip;

  @HiveField(20)
  final String? companyName;

  @HiveField(21)
  final String? category;

  @HiveField(22)
  final List<String>? tags;

  @HiveField(23)
  final String? kanbanState;

  @HiveField(24)
  final int? color;

  @HiveField(25)
  final DateTime? dateDeadline;

  @HiveField(26)
  final DateTime? dateLastStageUpdate;

  @HiveField(27)
  final DateTime? closeDate;

  @HiveField(28)
  final double? ratingLastValue;

  @HiveField(29)
  final DateTime? slaDeadline;

  @HiveField(30)
  final DateTime? slaReachedDatetime;

  @HiveField(31)
  final bool active;

  @HiveField(32)
  final String? email;

  @HiveField(33)
  final String? phone;

  const Ticket({
    required this.id,
    required this.name,
    this.description,
    required this.stage,
    required this.priority,
    this.partnerId,
    this.partnerName,
    this.userId,
    this.userName,
    required this.createDate,
    required this.writeDate,
    this.teamId,
    this.teamName,
    required this.active,
    this.email,
    this.phone,
    // الحقول الجديدة
    this.mobile,
    this.street,
    this.street2,
    this.city,
    this.state,
    this.country,
    this.zip,
    this.companyName,
    this.category,
    this.tags,
    this.kanbanState,
    this.color,
    this.dateDeadline,
    this.dateLastStageUpdate,
    this.closeDate,
    this.ratingLastValue,
    this.slaDeadline,
    this.slaReachedDatetime,
  });

  factory Ticket.fromJson(Map<String, dynamic> json) => _$TicketFromJson(json);
  Map<String, dynamic> toJson() => _$TicketToJson(this);

  Ticket copyWith({
    int? id,
    String? name,
    String? description,
    String? stage,
    String? priority,
    int? partnerId,
    String? partnerName,
    int? userId,
    String? userName,
    DateTime? createDate,
    DateTime? writeDate,
    int? teamId,
    String? teamName,
    bool? active,
    String? email,
    String? phone,
    String? mobile,
    String? street,
    String? street2,
    String? city,
    String? state,
    String? country,
    String? zip,
    String? companyName,
    String? category,
    List<String>? tags,
    String? kanbanState,
    int? color,
    DateTime? dateDeadline,
    DateTime? dateLastStageUpdate,
    DateTime? closeDate,
    double? ratingLastValue,
    DateTime? slaDeadline,
    DateTime? slaReachedDatetime,
  }) {
    return Ticket(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      stage: stage ?? this.stage,
      priority: priority ?? this.priority,
      partnerId: partnerId ?? this.partnerId,
      partnerName: partnerName ?? this.partnerName,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      createDate: createDate ?? this.createDate,
      writeDate: writeDate ?? this.writeDate,
      teamId: teamId ?? this.teamId,
      teamName: teamName ?? this.teamName,
      active: active ?? this.active,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      mobile: mobile ?? this.mobile,
      street: street ?? this.street,
      street2: street2 ?? this.street2,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      zip: zip ?? this.zip,
      companyName: companyName ?? this.companyName,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      kanbanState: kanbanState ?? this.kanbanState,
      color: color ?? this.color,
      dateDeadline: dateDeadline ?? this.dateDeadline,
      dateLastStageUpdate: dateLastStageUpdate ?? this.dateLastStageUpdate,
      closeDate: closeDate ?? this.closeDate,
      ratingLastValue: ratingLastValue ?? this.ratingLastValue,
      slaDeadline: slaDeadline ?? this.slaDeadline,
      slaReachedDatetime: slaReachedDatetime ?? this.slaReachedDatetime,
    );
  }

  // إزالة وسوم HTML من الوصف
  String? get cleanDescription {
    if (description == null || description!.isEmpty) return null;

    return description!
        .replaceAll(RegExp(r'<[^>]*>'), '') // إزالة وسوم HTML
        .replaceAll(RegExp(r'&nbsp;'), ' ') // استبدال &nbsp; بمسافة
        .replaceAll(RegExp(r'&amp;'), '&') // استبدال &amp; بـ &
        .replaceAll(RegExp(r'&lt;'), '<') // استبدال &lt; بـ <
        .replaceAll(RegExp(r'&gt;'), '>') // استبدال &gt; بـ >
        .replaceAll(RegExp(r'&quot;'), '"') // استبدال &quot; بـ "
        .replaceAll(
          RegExp(r'\s+'),
          ' ',
        ) // استبدال المسافات المتعددة بمسافة واحدة
        .trim();
  }

  String get priorityText {
    switch (priority) {
      case '0':
        return 'منخفضة';
      case '1':
        return 'عادية';
      case '2':
        return 'عالية';
      case '3':
        return 'عاجلة';
      default:
        return 'غير محدد';
    }
  }

  String get stageText {
    switch (stage) {
      case 'new':
        return 'جديد';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'solved':
        return 'تم الحل';
      case 'cancelled':
        return 'ملغي';
      default:
        return stage ?? 'غير محدد';
    }
  }

  // العنوان الكامل
  String? get fullAddress {
    final addressParts = <String>[];

    if (street != null && street!.isNotEmpty) {
      addressParts.add(street!);
    }
    if (street2 != null && street2!.isNotEmpty) {
      addressParts.add(street2!);
    }
    if (city != null && city!.isNotEmpty) {
      addressParts.add(city!);
    }
    if (state != null && state!.isNotEmpty) {
      addressParts.add(state!);
    }
    if (country != null && country!.isNotEmpty) {
      addressParts.add(country!);
    }
    if (zip != null && zip!.isNotEmpty) {
      addressParts.add(zip!);
    }

    return addressParts.isEmpty ? null : addressParts.join(', ');
  }

  // رقم الهاتف المفضل (الجوال أولاً ثم الهاتف الثابت)
  String? get preferredPhone {
    if (mobile != null && mobile!.isNotEmpty) {
      return mobile;
    }
    return phone;
  }

  // حالة SLA
  String get slaStatus {
    if (slaDeadline == null) return 'غير محدد';

    final now = DateTime.now();
    if (slaReachedDatetime != null) {
      return 'تم الوصول';
    } else if (now.isAfter(slaDeadline!)) {
      return 'متأخر';
    } else {
      final remaining = slaDeadline!.difference(now);
      if (remaining.inHours < 24) {
        return 'عاجل (${remaining.inHours} ساعة)';
      } else {
        return 'في الوقت المحدد (${remaining.inDays} يوم)';
      }
    }
  }

  // تقييم النجوم
  String get ratingStars {
    if (ratingLastValue == null) return 'غير مقيم';

    final rating = ratingLastValue!.round();
    return '★' * rating + '☆' * (5 - rating);
  }

  // الوسوم كنص
  String? get tagsText {
    if (tags == null || tags!.isEmpty) return null;
    return tags!.join(', ');
  }
}
