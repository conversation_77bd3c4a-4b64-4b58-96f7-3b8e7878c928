import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../models/ticket.dart';
import '../providers/tickets_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/connectivity_provider.dart';
import '../widgets/ticket_messages_widget.dart';

class TicketDetailScreen extends ConsumerStatefulWidget {
  final int ticketId;

  const TicketDetailScreen({super.key, required this.ticketId});

  @override
  ConsumerState<TicketDetailScreen> createState() => _TicketDetailScreenState();
}

class _TicketDetailScreenState extends ConsumerState<TicketDetailScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isEditing = false;
  bool _isLoading = false;

  final Map<String, String> _stages = {
    'new': 'جديد',
    'in_progress': 'قيد التنفيذ',
    'solved': 'تم الحل',
    'cancelled': 'ملغي',
  };

  final Map<String, String> _priorities = {
    '0': 'منخفضة',
    '1': 'عادية',
    '2': 'عالية',
    '3': 'عاجلة',
  };

  @override
  Widget build(BuildContext context) {
    final ticket = ref.watch(ticketProvider(widget.ticketId));
    final currentUser = ref.watch(currentUserProvider);
    final isConnected = ref.watch(connectivityProvider);

    // تطبيق الاتجاه من اليمين لليسار
    return Directionality(
      textDirection: TextDirection.rtl,
      child: _buildTicketDetail(context, ticket, currentUser, isConnected),
    );
  }

  Widget _buildTicketDetail(
    BuildContext context,
    Ticket? ticket,
    currentUser,
    bool isConnected,
  ) {
    if (ticket == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('تفاصيل التذكرة')),
        body: const Center(child: Text('التذكرة غير موجودة')),
      );
    }

    final canEdit = currentUser?.canEditTickets == true && isConnected;

    return Scaffold(
      appBar: AppBar(
        title: Text('تذكرة #${ticket.id}'),
        actions: [
          if (canEdit && !_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _isLoading ? null : _saveChanges,
            ),
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.cancel),
              onPressed:
                  _isLoading
                      ? null
                      : () {
                        setState(() {
                          _isEditing = false;
                        });
                      },
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: _isEditing ? _buildEditForm(ticket) : _buildViewMode(ticket),
      ),
    );
  }

  Widget _buildViewMode(Ticket ticket) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // رقم التذكرة والحالة (بارز)
        Card(
          elevation: 4,
          color: Theme.of(context).colorScheme.primaryContainer,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.confirmation_number,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تذكرة رقم #${ticket.id}',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color:
                              Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          _buildStatusChip(
                            ticket.stageText,
                            _getStageColor(ticket.stage),
                          ),
                          const SizedBox(width: 8),
                          _buildStatusChip(
                            ticket.priorityText,
                            _getPriorityColor(ticket.priority),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // معلومات أساسية
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('المعلومات الأساسية', Icons.info_outline),
                const SizedBox(height: 16),
                _buildEnhancedInfoRow('العنوان', ticket.name, Icons.title),
                if (ticket.partnerName != null)
                  _buildEnhancedInfoRow(
                    'العميل',
                    ticket.partnerName!,
                    Icons.person,
                  ),
                if (ticket.userName != null)
                  _buildEnhancedInfoRow(
                    'المسؤول',
                    ticket.userName!,
                    Icons.person_pin,
                  ),
                if (ticket.teamName != null)
                  _buildEnhancedInfoRow(
                    'الفريق',
                    ticket.teamName!,
                    Icons.group,
                  ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // الوصف
        if (ticket.description != null && ticket.description!.isNotEmpty)
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionHeader('الوصف التفصيلي', Icons.description),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest
                          .withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(
                          context,
                        ).colorScheme.outline.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Text(
                      ticket.cleanDescription ?? ticket.description!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.6,
                        fontSize: 15,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        const SizedBox(height: 16),

        // معلومات العميل التفصيلية
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('معلومات العميل', Icons.person_outline),
                const SizedBox(height: 16),
                if (ticket.partnerName != null)
                  _buildEnhancedInfoRow(
                    'اسم العميل',
                    ticket.partnerName!,
                    Icons.person,
                  ),
                if (ticket.companyName != null)
                  _buildEnhancedInfoRow(
                    'الشركة',
                    ticket.companyName!,
                    Icons.business,
                  ),
                if (ticket.email != null)
                  _buildEnhancedInfoRow(
                    'البريد الإلكتروني',
                    ticket.email!,
                    Icons.email,
                  ),
                if (ticket.preferredPhone != null)
                  _buildEnhancedInfoRow(
                    'رقم الهاتف',
                    ticket.preferredPhone!,
                    Icons.phone,
                  ),
                if (ticket.mobile != null &&
                    ticket.phone != null &&
                    ticket.mobile != ticket.phone)
                  _buildEnhancedInfoRow(
                    'الهاتف الثابت',
                    ticket.phone!,
                    Icons.phone_in_talk,
                  ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // العنوان
        if (ticket.fullAddress != null)
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionHeader('العنوان', Icons.location_on),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest
                          .withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(
                          context,
                        ).colorScheme.outline.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Text(
                      ticket.fullAddress!,
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(height: 1.5),
                    ),
                  ),
                ],
              ),
            ),
          ),
        const SizedBox(height: 16),

        // التواريخ
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('التواريخ المهمة', Icons.schedule),
                const SizedBox(height: 16),
                _buildEnhancedInfoRow(
                  'تاريخ الإنشاء',
                  _formatDateTime(ticket.createDate),
                  Icons.calendar_today,
                ),
                _buildEnhancedInfoRow(
                  'آخر تحديث',
                  _formatDateTime(ticket.writeDate),
                  Icons.update,
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // معلومات إضافية
        if (ticket.category != null ||
            ticket.tagsText != null ||
            ticket.ratingLastValue != null)
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionHeader('معلومات إضافية', Icons.info),
                  const SizedBox(height: 16),
                  if (ticket.category != null)
                    _buildEnhancedInfoRow(
                      'الفئة',
                      ticket.category!,
                      Icons.category,
                    ),
                  if (ticket.tagsText != null)
                    _buildEnhancedInfoRow(
                      'الوسوم',
                      ticket.tagsText!,
                      Icons.label,
                    ),
                  if (ticket.ratingLastValue != null)
                    _buildEnhancedInfoRow(
                      'التقييم',
                      ticket.ratingStars,
                      Icons.star,
                    ),
                  if (ticket.slaDeadline != null)
                    _buildEnhancedInfoRow(
                      'حالة SLA',
                      ticket.slaStatus,
                      Icons.timer,
                    ),
                ],
              ),
            ),
          ),
        const SizedBox(height: 16),

        // الرسائل والمرفقات
        TicketMessagesWidget(ticketId: ticket.id),
      ],
    );
  }

  Widget _buildEditForm(Ticket ticket) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تعديل التذكرة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // الحالة
                  FormBuilderDropdown<String>(
                    name: 'stage',
                    initialValue: ticket.stage,
                    decoration: const InputDecoration(
                      labelText: 'الحالة',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        _stages.entries
                            .map(
                              (entry) => DropdownMenuItem(
                                value: entry.key,
                                child: Text(entry.value),
                              ),
                            )
                            .toList(),
                    validator: FormBuilderValidators.required(
                      errorText: 'هذا الحقل مطلوب',
                    ),
                  ),
                  const SizedBox(height: 16),

                  // الأولوية
                  FormBuilderDropdown<String>(
                    name: 'priority',
                    initialValue: ticket.priority,
                    decoration: const InputDecoration(
                      labelText: 'الأولوية',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        _priorities.entries
                            .map(
                              (entry) => DropdownMenuItem(
                                value: entry.key,
                                child: Text(entry.value),
                              ),
                            )
                            .toList(),
                    validator: FormBuilderValidators.required(
                      errorText: 'هذا الحقل مطلوب',
                    ),
                  ),
                  const SizedBox(height: 16),

                  // الوصف
                  FormBuilderTextField(
                    name: 'description',
                    initialValue: ticket.description ?? '',
                    decoration: const InputDecoration(
                      labelText: 'الوصف',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 5,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveChanges() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      final values = _formKey.currentState!.value;
      final updateData = <String, dynamic>{};

      // تحديد القيم المتغيرة فقط
      if (values['stage'] != null) {
        updateData['stage_id'] = values['stage'];
      }
      if (values['priority'] != null) {
        updateData['priority'] = values['priority'];
      }
      if (values['description'] != null) {
        updateData['description'] = values['description'];
      }

      try {
        final success = await ref
            .read(ticketsProvider.notifier)
            .updateTicket(widget.ticketId, updateData);

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ التغييرات بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          setState(() {
            _isEditing = false;
          });
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في حفظ التغييرات'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // بناء عنوان القسم مع أيقونة
  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary, size: 20),
        const SizedBox(width: 8),
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  // بناء صف معلومات محسن مع أيقونة
  Widget _buildEnhancedInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 18,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }

  // بناء شريحة الحالة
  Widget _buildStatusChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  // الحصول على لون الحالة
  Color _getStageColor(String? stage) {
    switch (stage) {
      case 'new':
        return Colors.blue.shade600; // جديد - أزرق
      case 'in_progress':
        return Colors.orange.shade600; // قيد التنفيذ - برتقالي
      case 'solved':
        return Colors.green.shade600; // تم الحل - أخضر
      case 'cancelled':
        return Colors.red.shade600; // ملغي - أحمر
      default:
        return Colors.grey.shade600; // افتراضي - رمادي
    }
  }

  // الحصول على لون الأولوية
  Color _getPriorityColor(String? priority) {
    switch (priority) {
      case '3':
        return Colors.red.shade700; // عاجلة - أحمر داكن
      case '2':
        return Colors.orange.shade600; // عالية - برتقالي
      case '1':
        return Colors.blue.shade600; // عادية - أزرق
      case '0':
        return Colors.green.shade600; // منخفضة - أخضر
      default:
        return Colors.grey.shade600; // افتراضي - رمادي
    }
  }
}
