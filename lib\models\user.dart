import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class User {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String email;

  @HiveField(3)
  final String? phone;

  @HiveField(4)
  final List<String> groups;

  @HiveField(5)
  final String? sessionId;

  @HiveField(6)
  final DateTime? lastLogin;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.groups,
    this.sessionId,
    this.lastLogin,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$User<PERSON>rom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    List<String>? groups,
    String? sessionId,
    DateTime? lastLogin,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      groups: groups ?? this.groups,
      sessionId: sessionId ?? this.sessionId,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  bool get canEditTickets => groups.contains('helpdesk_manager') || groups.contains('helpdesk_user');
  bool get isAdmin => groups.contains('base.group_system');
}
