# دليل المطور - تطبيق مرادي أودو

## 🏗️ بنية المشروع

### الملفات الأساسية

```
lib/
├── main.dart                 # نقطة البداية والتوجيه
├── models/                   # نماذج البيانات
│   ├── user.dart            # نموذج المستخدم
│   ├── user.g.dart          # ملف مولد للمستخدم
│   ├── ticket.dart          # نموذج التذكرة
│   └── ticket.g.dart        # ملف مولد للتذكرة
├── services/                # الخدمات
│   ├── odoo_service.dart    # خدمة الاتصال بـ Odoo
│   ├── local_storage_service.dart  # خدمة التخزين المحلي
│   └── sync_service.dart    # خدمة المزامنة
├── providers/               # مزودي الحالة (Riverpod)
│   ├── auth_provider.dart   # مزود المصادقة
│   ├── tickets_provider.dart # مزود التذاكر
│   └── connectivity_provider.dart # مزود الاتصال
├── screens/                 # الشاشات
│   ├── login_screen.dart    # شاشة تسجيل الدخول
│   ├── tickets_list_screen.dart # شاشة قائمة التذاكر
│   └── ticket_detail_screen.dart # شاشة تفاصيل التذكرة
└── widgets/                 # المكونات المساعدة
    ├── ticket_card.dart     # بطاقة التذكرة
    └── tickets_filter_sheet.dart # ورقة الفلترة
```

## 🔧 التقنيات المستخدمة

### إدارة الحالة - Riverpod
- **AuthProvider**: إدارة حالة المصادقة
- **TicketsProvider**: إدارة حالة التذاكر
- **ConnectivityProvider**: مراقبة حالة الاتصال

### التخزين المحلي - Hive
- **UserBox**: تخزين بيانات المستخدمين
- **TicketsBox**: تخزين التذاكر للعمل أوفلاين
- **SettingsBox**: تخزين الإعدادات والتفضيلات

### الاتصال بـ Odoo - JSON-RPC
- استخدام مكتبة Dio للطلبات
- معالجة الجلسات والمصادقة
- تحويل البيانات من/إلى JSON

## 🔐 نظام المصادقة

### تسجيل الدخول
```dart
// في OdooService
Future<User?> login(String emailOrPhone, String password) async {
  // معالجة البريد الإلكتروني/رقم الهاتف
  final processedEmail = _processEmailOrPhone(emailOrPhone);
  
  // طلب المصادقة
  final response = await _dio.post('/web/session/authenticate', data: {
    'jsonrpc': '2.0',
    'method': 'call',
    'params': {
      'db': database,
      'login': processedEmail,
      'password': password,
    },
  });
  
  // معالجة الاستجابة وإرجاع المستخدم
}
```

### معالجة أرقام الهواتف
```dart
String _processEmailOrPhone(String input) {
  if (!input.contains('@')) {
    return '$<EMAIL>';
  }
  return input;
}
```

## 📱 إدارة التذاكر

### جلب التذاكر
```dart
// في OdooService
Future<List<Ticket>> getTickets() async {
  final response = await _dio.post('/web/dataset/call_kw', data: {
    'jsonrpc': '2.0',
    'method': 'call',
    'params': {
      'model': 'helpdesk.ticket',
      'method': 'search_read',
      'args': [[]],
      'kwargs': {
        'fields': ['name', 'description', 'stage_id', 'priority', 'create_date'],
      },
    },
  });
  
  return (response.data['result'] as List)
      .map((json) => Ticket.fromJson(json))
      .toList();
}
```

### تحديث التذاكر
```dart
Future<bool> updateTicket(int ticketId, Map<String, dynamic> values) async {
  final response = await _dio.post('/web/dataset/call_kw', data: {
    'jsonrpc': '2.0',
    'method': 'call',
    'params': {
      'model': 'helpdesk.ticket',
      'method': 'write',
      'args': [[ticketId], values],
      'kwargs': {},
    },
  });
  
  return response.data['result'] == true;
}
```

## 🔄 نظام المزامنة (Offline-First)

### العمليات المؤجلة
```dart
class PendingOperation {
  final String id;
  final String type; // 'update', 'create', 'delete'
  final Map<String, dynamic> data;
  final DateTime timestamp;
  
  // تحويل إلى/من JSON للتخزين
}
```

### المزامنة التلقائية
```dart
// في SyncService
Future<void> syncData() async {
  if (_isSyncing) return;
  _isSyncing = true;
  
  try {
    // مزامنة العمليات المؤجلة
    await _syncPendingOperations();
    
    // جلب التحديثات من الخادم
    await _fetchUpdatesFromServer();
    
    _syncStatusController.add(true);
  } catch (e) {
    _syncStatusController.add(false);
  } finally {
    _isSyncing = false;
  }
}
```

## 🎨 التصميم والواجهة

### Material 3
```dart
// في main.dart
theme: ThemeData(
  useMaterial3: true,
  colorScheme: ColorScheme.fromSeed(
    seedColor: const Color(0xFF1976D2),
    brightness: Brightness.light,
  ),
  // تخصيصات إضافية...
),
```

### الدعم العربي
- استخدام النصوص العربية في الواجهة
- دعم RTL (من اليمين إلى اليسار)
- تنسيق التواريخ بالعربية

## 🧪 الاختبار والتطوير

### تشغيل التطبيق
```bash
# تنظيف المشروع
flutter clean

# تثبيت المكتبات
flutter pub get

# إنشاء الملفات المولدة
flutter packages pub run build_runner build

# تشغيل على الويب
flutter run -d chrome

# تشغيل على الأندرويد
flutter run -d android
```

### إنشاء الملفات المولدة
```bash
# إنشاء ملفات Hive و JSON
flutter packages pub run build_runner build

# إنشاء مع حذف الملفات المتضاربة
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في build_runner**
   ```bash
   flutter packages pub run build_runner clean
   flutter packages pub run build_runner build --delete-conflicting-outputs
   ```

2. **مشاكل الاتصال بـ Odoo**
   - تحقق من رابط الخادم في `odoo_service.dart`
   - تأكد من صحة اسم قاعدة البيانات
   - فحص بيانات المصادقة

3. **مشاكل التخزين المحلي**
   ```dart
   // مسح البيانات المحلية
   await LocalStorageService.clearAllData();
   ```

## 📝 إضافة ميزات جديدة

### إضافة نموذج جديد
1. إنشاء ملف النموذج في `lib/models/`
2. إضافة تعليقات Hive و JSON
3. تشغيل build_runner
4. إضافة النموذج إلى LocalStorageService

### إضافة شاشة جديدة
1. إنشاء ملف الشاشة في `lib/screens/`
2. إضافة التوجيه في `main.dart`
3. إنشاء مزود الحالة إذا لزم الأمر

### إضافة API جديد
1. إضافة الطريقة في `OdooService`
2. إضافة معالجة الأخطاء
3. إضافة دعم العمل أوفلاين في `SyncService`

## 🚀 النشر

### بناء للإنتاج
```bash
# بناء للويب
flutter build web

# بناء للأندرويد
flutter build apk --release

# بناء للـ iOS
flutter build ios --release
```

### متطلبات النشر
- تحديث إعدادات الخادم للإنتاج
- تفعيل HTTPS
- تحسين الأداء
- اختبار شامل على جميع المنصات
