import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ticket_message.dart';
import '../models/ticket_attachment.dart';
import '../services/unified_odoo_service.dart';
import 'package:url_launcher/url_launcher.dart';

class TicketMessagesWidget extends ConsumerStatefulWidget {
  final int ticketId;

  const TicketMessagesWidget({super.key, required this.ticketId});

  @override
  ConsumerState<TicketMessagesWidget> createState() =>
      _TicketMessagesWidgetState();
}

class _TicketMessagesWidgetState extends ConsumerState<TicketMessagesWidget> {
  final UnifiedOdooService _odooService = UnifiedOdooService();
  List<TicketMessage> _messages = [];
  List<TicketAttachment> _attachments = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadMessagesAndAttachments();
  }

  Future<void> _loadMessagesAndAttachments() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final messages = await _odooService.getTicketMessages(widget.ticketId);
      final attachments = await _odooService.getTicketAttachments(
        widget.ticketId,
      );

      setState(() {
        _messages = messages;
        _attachments = attachments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'خطأ في تحميل الرسائل والمرفقات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    if (_error != null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.error,
                size: 48,
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadMessagesAndAttachments,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // المرفقات
        if (_attachments.isNotEmpty) ...[
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.attach_file,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'المرفقات (${_attachments.length})',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ..._attachments.map(
                    (attachment) => _buildAttachmentItem(attachment),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],

        // الرسائل
        if (_messages.isNotEmpty) ...[
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.message,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'الرسائل والتعليقات (${_messages.length})',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ..._messages.map((message) => _buildMessageItem(message)),
                ],
              ),
            ),
          ),
        ],

        // رسالة عدم وجود محتوى
        if (_messages.isEmpty && _attachments.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.inbox_outlined,
                    size: 48,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'لا توجد رسائل أو مرفقات',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAttachmentItem(TicketAttachment attachment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Text(attachment.fileIcon, style: const TextStyle(fontSize: 24)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  attachment.name,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${attachment.formattedFileSize} • ${attachment.mimetype}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _downloadAttachment(attachment),
            icon: const Icon(Icons.download),
            tooltip: 'تحميل',
          ),
        ],
      ),
    );
  }

  Widget _buildMessageItem(TicketMessage message) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            message.isComment
                ? Theme.of(
                  context,
                ).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3)
                : Theme.of(
                  context,
                ).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الرسالة
          Row(
            children: [
              Icon(
                message.isComment
                    ? Icons.comment
                    : message.isEmail
                    ? Icons.email
                    : Icons.notifications,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  message.authorName ?? 'مستخدم غير معروف',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
              ),
              Text(
                _formatDate(message.date),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),

          // موضوع الرسالة
          if (message.subject.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              message.subject,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
          ],

          // محتوى الرسالة
          if (message.cleanBody.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              message.cleanBody,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],

          // المرفقات في الرسالة
          if (message.hasAttachments) ...[
            const SizedBox(height: 8),
            Text(
              'المرفقات: ${message.attachmentIds.length}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  Future<void> _downloadAttachment(TicketAttachment attachment) async {
    try {
      final downloadUrl = await _odooService.downloadAttachment(attachment.id);
      if (downloadUrl != null) {
        final uri = Uri.parse(downloadUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          _showError('لا يمكن فتح الرابط');
        }
      } else {
        _showError('فشل في إنشاء رابط التحميل');
      }
    } catch (e) {
      _showError('خطأ في تحميل المرفق: $e');
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }
}
