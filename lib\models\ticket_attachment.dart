import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ticket_attachment.g.dart';

@HiveType(typeId: 4)
@JsonSerializable()
class TicketAttachment {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String mimetype;

  @HiveField(3)
  final int fileSize;

  @HiveField(4)
  final String? url;

  @HiveField(5)
  final int resId;

  @HiveField(6)
  final String resModel;

  @HiveField(7)
  final DateTime createDate;

  const TicketAttachment({
    required this.id,
    required this.name,
    required this.mimetype,
    required this.fileSize,
    this.url,
    required this.resId,
    required this.resModel,
    required this.createDate,
  });

  factory TicketAttachment.fromJson(Map<String, dynamic> json) =>
      _$TicketAttachmentFromJson(json);
  Map<String, dynamic> toJson() => _$TicketAttachmentToJson(this);

  // تحديد نوع الملف
  bool get isImage => mimetype.startsWith('image/');
  bool get isPdf => mimetype == 'application/pdf';
  bool get isDocument => mimetype.contains('document') || 
                        mimetype.contains('word') || 
                        mimetype.contains('text');
  bool get isVideo => mimetype.startsWith('video/');
  bool get isAudio => mimetype.startsWith('audio/');

  // تنسيق حجم الملف
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // أيقونة الملف حسب النوع
  String get fileIcon {
    if (isImage) return '🖼️';
    if (isPdf) return '📄';
    if (isDocument) return '📝';
    if (isVideo) return '🎥';
    if (isAudio) return '🎵';
    return '📎';
  }
}
