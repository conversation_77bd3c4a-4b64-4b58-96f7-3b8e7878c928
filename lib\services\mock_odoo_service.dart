import 'dart:async';
import '../models/user.dart';
import '../models/ticket.dart';

/// خدمة محاكاة Odoo للتطوير والاختبار
/// تستخدم هذه الخدمة عندما لا يمكن الوصول إلى خادم Odoo الحقيقي
class MockOdooService {
  static const String baseUrl = 'https://mrady.odoo.com';
  static const String database = 'mrady';

  // بيانات وهمية للمستخدمين
  static final Map<String, Map<String, dynamic>> _mockUsers = {
    '<EMAIL>': {
      'password': '123456',
      'user': User(
        id: 1,
        name: 'مدير النظام',
        email: '<EMAIL>',
        groups: ['base.group_system', 'helpdesk.group_helpdesk_manager'],
      ),
    },
    '<EMAIL>': {
      'password': '123456',
      'user': User(
        id: 2,
        name: 'مستخدم عادي',
        email: '<EMAIL>',
        groups: ['base.group_user', 'helpdesk.group_helpdesk_user'],
      ),
    },
    '<EMAIL>': {
      'password': '123456',
      'user': User(
        id: 3,
        name: 'مستخدم تجريبي',
        email: '<EMAIL>',
        groups: ['base.group_user', 'helpdesk.group_helpdesk_user'],
      ),
    },
  };

  // بيانات وهمية للتذاكر
  static final List<Ticket> _mockTickets = [
    Ticket(
      id: 1,
      name: 'مشكلة في تسجيل الدخول',
      description: 'لا أستطيع تسجيل الدخول إلى النظام',
      stage: 'new',
      priority: '1',
      partnerId: 1,
      partnerName: 'عميل تجريبي',
      userId: 1,
      userName: 'مدير النظام',
      createDate: DateTime.now().subtract(const Duration(hours: 2)),
      writeDate: DateTime.now().subtract(const Duration(minutes: 30)),
      active: true,
    ),
    Ticket(
      id: 2,
      name: 'طلب تحديث البيانات',
      description: 'أحتاج إلى تحديث بيانات الحساب',
      stage: 'in_progress',
      priority: '2',
      partnerId: 2,
      partnerName: 'عميل آخر',
      userId: 2,
      userName: 'مستخدم عادي',
      createDate: DateTime.now().subtract(const Duration(days: 1)),
      writeDate: DateTime.now().subtract(const Duration(hours: 1)),
      active: true,
    ),
    Ticket(
      id: 3,
      name: 'استفسار عن الخدمات',
      description: 'أريد معرفة المزيد عن الخدمات المتاحة',
      stage: 'solved',
      priority: '0',
      partnerId: 3,
      partnerName: 'عميل جديد',
      userId: 1,
      userName: 'مدير النظام',
      createDate: DateTime.now().subtract(const Duration(days: 3)),
      writeDate: DateTime.now().subtract(const Duration(days: 1)),
      active: true,
    ),
  ];

  String? _currentUserEmail;

  // معالجة البريد الإلكتروني/رقم الهاتف
  String _processEmailOrPhone(String input) {
    if (!input.contains('@')) {
      return '$<EMAIL>';
    }
    return input;
  }

  // تسجيل الدخول
  Future<User?> login(String emailOrPhone, String password) async {
    // محاكاة تأخير الشبكة
    await Future.delayed(const Duration(seconds: 1));

    final processedEmail = _processEmailOrPhone(emailOrPhone);

    if (_mockUsers.containsKey(processedEmail)) {
      final userData = _mockUsers[processedEmail]!;
      if (userData['password'] == password) {
        _currentUserEmail = processedEmail;
        return userData['user'] as User;
      } else {
        throw Exception('كلمة المرور غير صحيحة');
      }
    } else {
      throw Exception('المستخدم غير موجود');
    }
  }

  // جلب معلومات المستخدم
  Future<User?> getUserInfo() async {
    await Future.delayed(const Duration(milliseconds: 500));

    if (_currentUserEmail != null &&
        _mockUsers.containsKey(_currentUserEmail)) {
      return _mockUsers[_currentUserEmail]!['user'] as User;
    }
    return null;
  }

  // جلب أسماء المجموعات
  Future<List<String>> getGroupNames(List<int> groupIds) async {
    await Future.delayed(const Duration(milliseconds: 300));

    // مجموعات وهمية
    final groups = {
      1: 'base.group_user',
      2: 'base.group_system',
      3: 'helpdesk.group_helpdesk_user',
      4: 'helpdesk.group_helpdesk_manager',
    };

    return groupIds.map((id) => groups[id] ?? 'unknown_group').toList();
  }

  // جلب التذاكر
  Future<List<Ticket>> getTickets({String? searchQuery, int limit = 80}) async {
    await Future.delayed(const Duration(seconds: 1));

    List<Ticket> tickets = List.from(_mockTickets);

    // تطبيق البحث إذا تم توفيره
    if (searchQuery != null && searchQuery.trim().isNotEmpty) {
      final query = searchQuery.toLowerCase();
      tickets =
          tickets.where((ticket) {
            return ticket.name.toLowerCase().contains(query) ||
                (ticket.description?.toLowerCase().contains(query) ?? false);
          }).toList();
    }

    // ترتيب حسب ID (الأقدم أولاً)
    tickets.sort((a, b) => a.id.compareTo(b.id));

    // تطبيق الحد الأقصى
    if (tickets.length > limit) {
      tickets = tickets.take(limit).toList();
    }

    return tickets;
  }

  // تحديث تذكرة
  Future<bool> updateTicket(int ticketId, Map<String, dynamic> values) async {
    await Future.delayed(const Duration(milliseconds: 800));

    // البحث عن التذكرة وتحديثها
    final ticketIndex = _mockTickets.indexWhere(
      (ticket) => ticket.id == ticketId,
    );
    if (ticketIndex != -1) {
      final ticket = _mockTickets[ticketIndex];

      // تحديث الحقول
      _mockTickets[ticketIndex] = ticket.copyWith(
        stage: values['stage_id']?.toString() ?? ticket.stage,
        priority: values['priority']?.toString() ?? ticket.priority,
        description: values['description'] ?? ticket.description,
        writeDate: DateTime.now(),
      );

      return true;
    }

    return false;
  }

  // تسجيل الخروج
  Future<void> logout() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _currentUserEmail = null;
  }

  // التحقق من صحة الجلسة
  Future<bool> validateSession() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _currentUserEmail != null;
  }
}
