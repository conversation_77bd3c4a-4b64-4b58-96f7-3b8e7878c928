// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:mrady_odoo/main.dart';

void main() {
  testWidgets('App loads and shows login screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: MradyOdooApp()));

    // Wait for the app to load
    await tester.pumpAndSettle();

    // Verify that login screen elements are present
    expect(find.text('مرحباً بك في مرادي أودو'), findsOneWidget);
    expect(find.text('البريد الإلكتروني أو رقم الهاتف'), findsOneWidget);
    expect(find.text('كلمة المرور'), findsOneWidget);
    expect(find.text('تسجيل الدخول'), findsOneWidget);
  });

  testWidgets('Email processing works correctly', (WidgetTester tester) async {
    // Test email processing logic
    const phoneNumber = '123456789';
    // const email = '<EMAIL>'; // للاستخدام المستقبلي

    // This would test the _processEmailOrPhone method if it was public
    // For now, we'll test the UI behavior

    await tester.pumpWidget(const ProviderScope(child: MradyOdooApp()));
    await tester.pumpAndSettle();

    // Find the email field and enter a phone number
    final emailField = find.byKey(const Key('email_field'));
    expect(emailField, findsOneWidget);

    await tester.enterText(emailField, phoneNumber);
    await tester.pump();

    // Verify the text was entered
    expect(find.text(phoneNumber), findsOneWidget);
  });
}
