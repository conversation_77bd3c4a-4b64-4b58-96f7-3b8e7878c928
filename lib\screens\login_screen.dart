import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../providers/auth_provider.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final isLoading = authState.state == AuthState.loading;

    // تطبيق الاتجاه من اليمين لليسار
    return Directionality(
      textDirection: TextDirection.rtl,
      child: _buildLoginScreen(context, authState, isLoading),
    );
  }

  Widget _buildLoginScreen(
    BuildContext context,
    AuthStateData authState,
    bool isLoading,
  ) {
    // الاستماع لتغييرات حالة المصادقة
    ref.listen<AuthStateData>(authProvider, (previous, next) {
      if (next.state == AuthState.error && next.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text(next.errorMessage!)),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إغلاق',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
                ref.read(authProvider.notifier).clearError();
              },
            ),
          ),
        );
      }
    });

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primaryContainer,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Card(
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // شعار التطبيق
                      Icon(
                        Icons.support_agent,
                        size: 80,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'مرادي أودو',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'نظام إدارة التذاكر',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 32),

                      // نموذج تسجيل الدخول
                      FormBuilder(
                        key: _formKey,
                        child: Column(
                          children: [
                            // حقل البريد الإلكتروني أو رقم الهاتف
                            FormBuilderTextField(
                              key: const Key('email_field'),
                              name: 'emailOrPhone',
                              decoration: InputDecoration(
                                labelText: 'البريد الإلكتروني أو رقم الهاتف',
                                hintText:
                                    'أدخل البريد الإلكتروني أو رقم الهاتف',
                                prefixIcon: const Icon(Icons.person),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(
                                  errorText: 'هذا الحقل مطلوب',
                                ),
                                FormBuilderValidators.minLength(
                                  3,
                                  errorText: 'يجب أن يكون على الأقل 3 أحرف',
                                ),
                              ]),
                              textInputAction: TextInputAction.next,
                            ),
                            const SizedBox(height: 16),

                            // حقل كلمة المرور
                            FormBuilderTextField(
                              name: 'password',
                              obscureText: _obscurePassword,
                              decoration: InputDecoration(
                                labelText: 'كلمة المرور',
                                hintText: 'أدخل كلمة المرور',
                                prefixIcon: const Icon(Icons.lock),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscurePassword
                                        ? Icons.visibility
                                        : Icons.visibility_off,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(
                                  errorText: 'هذا الحقل مطلوب',
                                ),
                                FormBuilderValidators.minLength(
                                  6,
                                  errorText: 'يجب أن تكون على الأقل 6 أحرف',
                                ),
                              ]),
                              textInputAction: TextInputAction.done,
                              onSubmitted: (_) => _login(),
                            ),
                            const SizedBox(height: 16),

                            // خيار تذكرني
                            FormBuilderCheckbox(
                              name: 'rememberMe',
                              title: const Text('تذكرني'),
                              initialValue: _rememberMe,
                              onChanged: (value) {
                                setState(() {
                                  _rememberMe = value ?? false;
                                });
                              },
                            ),
                            const SizedBox(height: 24),

                            // زر تسجيل الدخول
                            SizedBox(
                              width: double.infinity,
                              height: 50,
                              child: ElevatedButton(
                                onPressed: isLoading ? null : _login,
                                style: ElevatedButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child:
                                    isLoading
                                        ? const SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                          ),
                                        )
                                        : const Text(
                                          'تسجيل الدخول',
                                          style: TextStyle(fontSize: 16),
                                        ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _login() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final values = _formKey.currentState!.value;
      final emailOrPhone = values['emailOrPhone'] as String;
      final password = values['password'] as String;
      final rememberMe = values['rememberMe'] as bool? ?? false;

      final success = await ref
          .read(authProvider.notifier)
          .login(emailOrPhone, password, rememberMe);

      if (success && mounted) {
        // الانتقال إلى الشاشة الرئيسية سيتم التعامل معه في main.dart
      }
    }
  }
}
