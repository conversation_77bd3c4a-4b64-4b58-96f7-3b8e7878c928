// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_message.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TicketMessageAdapter extends TypeAdapter<TicketMessage> {
  @override
  final int typeId = 3;

  @override
  TicketMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TicketMessage(
      id: fields[0] as int,
      subject: fields[1] as String,
      body: fields[2] as String,
      messageType: fields[3] as String,
      authorId: fields[4] as int?,
      authorName: fields[5] as String?,
      date: fields[6] as DateTime,
      resId: fields[7] as int,
      resModel: fields[8] as String,
      attachmentIds: (fields[9] as List).cast<int>(),
    );
  }

  @override
  void write(BinaryWriter writer, TicketMessage obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.subject)
      ..writeByte(2)
      ..write(obj.body)
      ..writeByte(3)
      ..write(obj.messageType)
      ..writeByte(4)
      ..write(obj.authorId)
      ..writeByte(5)
      ..write(obj.authorName)
      ..writeByte(6)
      ..write(obj.date)
      ..writeByte(7)
      ..write(obj.resId)
      ..writeByte(8)
      ..write(obj.resModel)
      ..writeByte(9)
      ..write(obj.attachmentIds);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TicketMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketMessage _$TicketMessageFromJson(Map<String, dynamic> json) =>
    TicketMessage(
      id: (json['id'] as num).toInt(),
      subject: json['subject'] as String,
      body: json['body'] as String,
      messageType: json['messageType'] as String,
      authorId: (json['authorId'] as num?)?.toInt(),
      authorName: json['authorName'] as String?,
      date: DateTime.parse(json['date'] as String),
      resId: (json['resId'] as num).toInt(),
      resModel: json['resModel'] as String,
      attachmentIds: (json['attachmentIds'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$TicketMessageToJson(TicketMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'subject': instance.subject,
      'body': instance.body,
      'messageType': instance.messageType,
      'authorId': instance.authorId,
      'authorName': instance.authorName,
      'date': instance.date.toIso8601String(),
      'resId': instance.resId,
      'resModel': instance.resModel,
      'attachmentIds': instance.attachmentIds,
    };
