# دليل اختبار تطبيق مرادي أودو

## 🚀 الوضع الحالي: **الخدمة الحقيقية مفعلة**

التطبيق الآن يتصل مباشرة بخادم Odoo الحقيقي على `https://mrady.odoo.com` باستخدام CORS proxy.

## 🧪 بيانات الاختبار

### حسابات المستخدمين الحقيقية

استخدم الحسابات الموجودة فعلاً في خادم Odoo:

#### معالجة أرقام الهواتف
- إذا أدخلت رقم هاتف بدون `@`، سيتم إضافة `@mrady.com` تلقائياً
- مثال: `123456789` سيصبح `<EMAIL>`

## 🔧 إعدادات الخدمة

### الخدمة الحقيقية (مفعلة حالياً)

في ملف `lib/services/unified_odoo_service.dart`:

```dart
static const bool _useMockService = false; // false = الخدمة الحقيقية
```

### التبديل للوضع التجريبي

لاستخدام البيانات الوهمية للاختبار:

```dart
static const bool _useMockService = true; // true = وضع المحاكاة
```

## 📱 سيناريوهات الاختبار

### 1. اختبار تسجيل الدخول

#### ✅ تسجيل دخول ناجح
1. افتح التطبيق
2. أدخل حساب موجود في خادم Odoo
3. اضغط "تسجيل الدخول"
4. **النتيجة المتوقعة**: الانتقال إلى شاشة التذاكر مع عرض التذاكر الحقيقية

#### ❌ تسجيل دخول فاشل
1. أدخل بريد إلكتروني غير موجود: `<EMAIL>`
2. أدخل كلمة مرور: `wrongpassword`
3. اضغط "تسجيل الدخول"
4. **النتيجة المتوقعة**: رسالة خطأ "بيانات تسجيل الدخول غير صحيحة"

#### ❌ كلمة مرور خاطئة
1. أدخل بريد إلكتروني موجود في النظام
2. أدخل كلمة مرور خاطئة: `wrongpassword`
3. اضغط "تسجيل الدخول"
4. **النتيجة المتوقعة**: رسالة خطأ "بيانات تسجيل الدخول غير صحيحة"

### 2. اختبار معالجة أرقام الهواتف

#### ✅ رقم هاتف بدون @
1. أدخل رقم هاتف موجود في النظام (بدون @mrady.com)
2. أدخل كلمة المرور الصحيحة
3. اضغط "تسجيل الدخول"
4. **النتيجة المتوقعة**: تسجيل دخول ناجح (سيتم إضافة @mrady.com تلقائياً)

#### ✅ بريد إلكتروني كامل
1. أدخل البريد الإلكتروني الكامل لحساب موجود
2. أدخل كلمة المرور الصحيحة
3. اضغط "تسجيل الدخول"
4. **النتيجة المتوقعة**: تسجيل دخول ناجح (لن يتم تعديل البريد)

### 3. اختبار إدارة التذاكر

#### عرض التذاكر
1. سجل الدخول بحساب صحيح
2. **النتيجة المتوقعة**:
   - عرض التذاكر الحقيقية من خادم Odoo
   - إحصائيات التذاكر في الأعلى
   - مؤشر حالة الاتصال

#### البحث في التذاكر
1. في شاشة التذاكر، اضغط على أيقونة البحث
2. أدخل كلمة بحث موجودة في التذاكر
3. **النتيجة المتوقعة**: عرض التذاكر المطابقة للبحث فقط

#### فلترة التذاكر
1. اضغط على أيقونة الفلترة
2. اختر حالة معينة من الحالات المتاحة
3. اضغط "تطبيق"
4. **النتيجة المتوقعة**: عرض التذاكر المطابقة للفلتر المحدد فقط

### 4. اختبار تفاصيل التذكرة

#### عرض التفاصيل
1. اضغط على أي تذكرة من القائمة
2. **النتيجة المتوقعة**: 
   - عرض تفاصيل كاملة للتذكرة
   - أزرار التعديل (حسب الصلاحيات)

#### تعديل التذكرة (حسب الصلاحيات)
1. سجل الدخول بحساب له صلاحيات التعديل
2. اضغط على تذكرة
3. اضغط "تعديل"
4. غير الحالة أو الأولوية
5. اضغط "حفظ"
6. **النتيجة المتوقعة**: حفظ التغييرات في خادم Odoo وعرض رسالة نجاح

### 5. اختبار العمل أوفلاين

#### محاكاة فقدان الاتصال
1. افتح أدوات المطور في المتصفح (F12)
2. اذهب إلى تبويب Network
3. اختر "Offline"
4. حاول تحديث التذاكر
5. **النتيجة المتوقعة**: 
   - عرض البيانات المحفوظة محلياً
   - مؤشر "غير متصل"

### 6. اختبار "تذكرني"

#### تفعيل تذكرني
1. في شاشة تسجيل الدخول، فعل "تذكرني"
2. سجل الدخول
3. أغلق التطبيق وأعد فتحه
4. **النتيجة المتوقعة**: تسجيل دخول تلقائي

#### إلغاء تذكرني
1. سجل الخروج
2. **النتيجة المتوقعة**: العودة إلى شاشة تسجيل الدخول

## 🐛 الأخطاء المتوقعة وحلولها

### مشكلة الاتصال بالخادم
- **الخطأ**: `خطأ في الاتصال بالخادم`
- **الحل**: تأكد من:
  - الاتصال بالإنترنت
  - أن خادم Odoo متاح على `https://mrady.odoo.com`
  - أن CORS proxy يعمل بشكل صحيح

### مشكلة بيانات تسجيل الدخول
- **الخطأ**: `بيانات تسجيل الدخول غير صحيحة`
- **الحل**: تأكد من:
  - صحة البريد الإلكتروني أو رقم الهاتف
  - صحة كلمة المرور
  - أن الحساب موجود في خادم Odoo

### مشكلة Hive في الاختبارات
- **الخطأ**: `You need to initialize Hive`
- **الحل**: هذا طبيعي في بيئة الاختبار، التطبيق يعمل بشكل صحيح في المتصفح

### بطء في التحميل
- **السبب**: الاتصال بخادم Odoo الحقيقي عبر الإنترنت
- **الحل**: هذا طبيعي، انتظر حتى اكتمال التحميل

## 📊 تقرير الاختبار

### ✅ الميزات التي تعمل
- [x] **الاتصال بخادم Odoo الحقيقي** باستخدام CORS proxy
- [x] تسجيل الدخول بالبريد الإلكتروني
- [x] تسجيل الدخول برقم الهاتف (مع إضافة @mrady.com تلقائياً)
- [x] معالجة أخطاء تسجيل الدخول مع رسائل واضحة
- [x] عرض رسائل خطأ باللغة العربية
- [x] عرض التذاكر الحقيقية من خادم Odoo
- [x] البحث والفلترة في التذاكر
- [x] تفاصيل التذكرة
- [x] تعديل التذاكر (حسب الصلاحيات في Odoo)
- [x] العمل أوفلاين مع البيانات المحفوظة محلياً
- [x] تذكر المستخدم
- [x] واجهة Material 3
- [x] دعم اللغة العربية RTL

### 🔄 للتطوير المستقبلي
- [ ] اختبارات وحدة شاملة
- [ ] تحسين الأداء
- [ ] المزيد من أنواع التذاكر
- [ ] إشعارات push
- [ ] تصدير التقارير

## 🚀 نشر التطبيق

عند الاستعداد للنشر:
1. غير `_useMockService` إلى `false`
2. تأكد من إعدادات خادم Odoo
3. اختبر الاتصال الحقيقي
4. قم ببناء التطبيق للإنتاج
