import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ticket_message.g.dart';

@HiveType(typeId: 3)
@JsonSerializable()
class TicketMessage {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String subject;

  @HiveField(2)
  final String body;

  @HiveField(3)
  final String messageType;

  @HiveField(4)
  final int? authorId;

  @HiveField(5)
  final String? authorName;

  @HiveField(6)
  final DateTime date;

  @HiveField(7)
  final int resId;

  @HiveField(8)
  final String resModel;

  @HiveField(9)
  final List<int> attachmentIds;

  const TicketMessage({
    required this.id,
    required this.subject,
    required this.body,
    required this.messageType,
    this.authorId,
    this.authorName,
    required this.date,
    required this.resId,
    required this.resModel,
    required this.attachmentIds,
  });

  factory TicketMessage.fromJson(Map<String, dynamic> json) =>
      _$TicketMessageFromJson(json);
  Map<String, dynamic> toJson() => _$TicketMessageToJson(this);

  // إزالة وسوم HTML من النص
  String get cleanBody {
    return body
        .replaceAll(RegExp(r'<[^>]*>'), '') // إزالة وسوم HTML
        .replaceAll(RegExp(r'&nbsp;'), ' ') // استبدال &nbsp; بمسافة
        .replaceAll(RegExp(r'&amp;'), '&') // استبدال &amp; بـ &
        .replaceAll(RegExp(r'&lt;'), '<') // استبدال &lt; بـ <
        .replaceAll(RegExp(r'&gt;'), '>') // استبدال &gt; بـ >
        .replaceAll(RegExp(r'&quot;'), '"') // استبدال &quot; بـ "
        .replaceAll(RegExp(r'\s+'), ' ') // استبدال المسافات المتعددة بمسافة واحدة
        .trim();
  }

  bool get isComment => messageType == 'comment';
  bool get isNotification => messageType == 'notification';
  bool get isEmail => messageType == 'email';

  bool get hasAttachments => attachmentIds.isNotEmpty;
}
