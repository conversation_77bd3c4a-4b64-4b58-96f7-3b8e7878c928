import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ticket.dart';
import '../providers/tickets_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/connectivity_provider.dart';
import '../widgets/ticket_card.dart';
import '../widgets/tickets_filter_sheet.dart';
import 'ticket_detail_screen.dart';

class TicketsListScreen extends ConsumerStatefulWidget {
  const TicketsListScreen({super.key});

  @override
  ConsumerState<TicketsListScreen> createState() => _TicketsListScreenState();
}

class _TicketsListScreenState extends ConsumerState<TicketsListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String? _selectedStage;
  String? _selectedPriority;
  Timer? _searchTimer;

  @override
  void dispose() {
    _searchController.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }

  // البحث المباشر مع تأخير لتجنب الكثير من الطلبات
  void _performLiveSearch(String query) {
    // إلغاء المؤقت السابق إن وجد
    _searchTimer?.cancel();

    // إنشاء مؤقت جديد
    _searchTimer = Timer(const Duration(milliseconds: 500), () {
      // تنفيذ البحث
      ref.read(ticketsProvider.notifier).searchTickets(query);
    });
  }

  @override
  Widget build(BuildContext context) {
    final ticketsState = ref.watch(ticketsProvider);
    final currentUser = ref.watch(currentUserProvider);
    final isConnected = ref.watch(connectivityProvider);
    final lastSyncTime = ref.watch(lastSyncTimeProvider);
    final ticketsStats = ref.watch(ticketsStatsProvider);

    // تطبيق الاتجاه من اليمين لليسار
    return Directionality(
      textDirection: TextDirection.rtl,
      child: _buildContent(
        context,
        ticketsState,
        currentUser,
        isConnected,
        lastSyncTime,
        ticketsStats,
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    TicketsStateData ticketsState,
    currentUser,
    bool isConnected,
    lastSyncTime,
    ticketsStats,
  ) {
    // فلترة التذاكر (فقط الفلاتر المحلية، البحث يتم على مستوى الخادم)
    List<Ticket> filteredTickets = ticketsState.tickets;

    // تطبيق فلتر الحالة
    if (_selectedStage != null) {
      filteredTickets =
          filteredTickets
              .where((ticket) => ticket.stage == _selectedStage)
              .toList();
    }

    // تطبيق فلتر الأولوية
    if (_selectedPriority != null) {
      filteredTickets =
          filteredTickets
              .where((ticket) => ticket.priority == _selectedPriority)
              .toList();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('التذاكر'),
        actions: [
          // زر تحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(ticketsProvider.notifier).refreshTickets();
            },
          ),
          // زر الفلترة
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterSheet(context),
          ),
          // زر تسجيل الخروج
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'logout') {
                _showLogoutDialog(context);
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        const Icon(Icons.logout),
                        const SizedBox(width: 8),
                        Text('تسجيل الخروج'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط المعلومات
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            child: Column(
              children: [
                // معلومات المستخدم والاتصال
                Row(
                  children: [
                    Icon(
                      isConnected ? Icons.cloud_done : Icons.cloud_off,
                      color: isConnected ? Colors.green : Colors.red,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isConnected ? 'متصل' : 'غير متصل',
                      style: TextStyle(
                        color: isConnected ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    if (lastSyncTime != null)
                      Text(
                        'آخر تحديث: ${_formatDateTime(lastSyncTime)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                // إحصائيات التذاكر
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatChip(
                        'المجموع',
                        ticketsStats['total'] ?? 0,
                        Colors.blue,
                      ),
                      _buildStatChip(
                        'جديد',
                        ticketsStats['new'] ?? 0,
                        Colors.green,
                      ),
                      _buildStatChip(
                        'قيد التنفيذ',
                        ticketsStats['in_progress'] ?? 0,
                        Colors.orange,
                      ),
                      _buildStatChip(
                        'عاجل',
                        ticketsStats['urgent_priority'] ?? 0,
                        Colors.red,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في التذاكر...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon:
                    _searchQuery.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _searchQuery = '';
                            });
                            // إعادة تحميل التذاكر العادية
                            ref.read(ticketsProvider.notifier).refreshTickets();
                          },
                        )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                // البحث المباشر مع تأخير قصير لتجنب الكثير من الطلبات
                _performLiveSearch(value);
              },
            ),
          ),

          // قائمة التذاكر
          Expanded(child: _buildTicketsList(filteredTickets, ticketsState)),
        ],
      ),
      floatingActionButton:
          currentUser?.canEditTickets == true
              ? FloatingActionButton(
                onPressed: () {
                  // إضافة تذكرة جديدة (يمكن تطويرها لاحقاً)
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('ميزة إضافة تذكرة جديدة قيد التطوير'),
                    ),
                  );
                },
                child: const Icon(Icons.add),
              )
              : null,
    );
  }

  Widget _buildStatChip(String label, int count, Color color) {
    return Chip(
      label: Text('$label: $count', style: const TextStyle(fontSize: 12)),
      backgroundColor: color.withValues(alpha: 0.1),
      side: BorderSide(color: color),
    );
  }

  Widget _buildTicketsList(
    List<Ticket> tickets,
    TicketsStateData ticketsState,
  ) {
    if (ticketsState.state == TicketsState.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (tickets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty ||
                      _selectedStage != null ||
                      _selectedPriority != null
                  ? 'لا توجد تذاكر تطابق البحث'
                  : 'لا توجد تذاكر متاحة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isNotEmpty ||
                      _selectedStage != null ||
                      _selectedPriority != null
                  ? 'جرب تغيير معايير البحث أو الفلترة'
                  : 'قد يكون السبب:\n• عدم وجود وحدة Helpdesk في النظام\n• عدم وجود تذاكر مُنشأة\n• عدم وجود صلاحيات للعرض',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(ticketsProvider.notifier).refreshTickets(),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: tickets.length,
        itemBuilder: (context, index) {
          final ticket = tickets[index];
          return TicketCard(
            ticket: ticket,
            onTap: () => _navigateToTicketDetail(ticket),
          );
        },
      ),
    );
  }

  void _navigateToTicketDetail(Ticket ticket) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TicketDetailScreen(ticketId: ticket.id),
      ),
    );
  }

  void _showFilterSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => TicketsFilterSheet(
            selectedStage: _selectedStage,
            selectedPriority: _selectedPriority,
            onApplyFilter: (stage, priority) {
              setState(() {
                _selectedStage = stage;
                _selectedPriority = priority;
              });
            },
          ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تسجيل الخروج'),
            content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref.read(authProvider.notifier).logout();
                },
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}
