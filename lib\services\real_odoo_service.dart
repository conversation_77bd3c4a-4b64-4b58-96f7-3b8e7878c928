import 'package:dio/dio.dart';
import '../models/user.dart';
import '../models/ticket.dart';
import '../models/ticket_message.dart';
import '../models/ticket_attachment.dart';

/// خدمة Odoo الحقيقية - اتصال مباشر (للأجهزة المحمولة فقط)
class RealOdooService {
  static const String baseUrl = 'https://mrady.odoo.com';
  static const String database = 'mrady';

  late final Dio _dio;
  int? _userId;
  String? _password;
  String? _email;

  RealOdooService() {
    _dio = Dio();
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.headers = {'Content-Type': 'application/json'};
  }

  // معالجة البريد الإلكتروني/رقم الهاتف
  String _processEmailOrPhone(String input) {
    if (!input.contains('@')) {
      return '$<EMAIL>';
    }
    return input;
  }

  // JSON-RPC Helper - مباشر بدون CORS proxy (للأجهزة المحمولة فقط)
  Future<dynamic> _odooJsonRpc(
    String method,
    Map<String, dynamic> params,
  ) async {
    final endpoint = '$baseUrl/jsonrpc';

    try {
      final response = await _dio.post(
        endpoint,
        data: {
          'jsonrpc': '2.0',
          'method': method,
          'params': params,
          'id': DateTime.now().millisecondsSinceEpoch,
        },
      );

      if (response.data['error'] != null) {
        final error = response.data['error'];
        String errorMessage = error['message'] ?? 'خطأ غير معروف';
        if (error['data'] != null && error['data']['debug'] != null) {
          errorMessage += ' - ${error['data']['debug']}';
        }
        throw Exception(errorMessage);
      }

      return response.data['result'];
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionError) {
        throw Exception('خطأ في الاتصال بالخادم. تأكد من الاتصال بالإنترنت');
      } else if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception('انتهت مهلة الاتصال. حاول مرة أخرى');
      } else if (e.response?.statusCode == 404) {
        throw Exception('الخادم غير متاح حالياً');
      } else if (e.response?.statusCode == 500) {
        throw Exception('خطأ في الخادم الداخلي');
      } else {
        throw Exception('خطأ في الشبكة: ${e.message}');
      }
    }
  }

  // تسجيل الدخول
  Future<User?> login(String emailOrPhone, String password) async {
    try {
      final processedEmail = _processEmailOrPhone(emailOrPhone);

      // محاولة المصادقة باستخدام JSON-RPC
      final uid = await _odooJsonRpc('call', {
        'service': 'common',
        'method': 'authenticate',
        'args': [database, processedEmail, password, {}],
      });

      if (uid == null || uid == false) {
        throw Exception(
          'بيانات تسجيل الدخول غير صحيحة. تأكد من البريد الإلكتروني وكلمة المرور',
        );
      }

      // حفظ معلومات الجلسة
      _userId = uid;
      _password = password;
      _email = processedEmail;

      // جلب معلومات المستخدم
      return await getUserInfo();
    } catch (e) {
      if (e is Exception) {
        rethrow;
      } else {
        throw Exception('خطأ غير متوقع: $e');
      }
    }
  }

  // جلب معلومات المستخدم
  Future<User?> getUserInfo() async {
    if (_userId == null || _password == null || _email == null) {
      return null;
    }

    try {
      // جلب بيانات المستخدم
      final userData = await _odooJsonRpc('call', {
        'service': 'object',
        'method': 'execute_kw',
        'args': [
          database,
          _userId,
          _password,
          'res.users',
          'read',
          [_userId],
          {
            'fields': ['name', 'email', 'groups_id'],
          },
        ],
      });

      if (userData != null && userData.isNotEmpty) {
        final user = userData[0];

        // جلب أسماء المجموعات
        List<String> groupNames = [];
        if (user['groups_id'] != null && user['groups_id'].isNotEmpty) {
          groupNames = await getGroupNames(List<int>.from(user['groups_id']));
        }

        return User(
          id: _userId!,
          name: user['name'] ?? 'مستخدم غير معروف',
          email: user['email'] ?? _email!,
          groups: groupNames,
        );
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  // جلب أسماء المجموعات
  Future<List<String>> getGroupNames(List<int> groupIds) async {
    if (_userId == null || _password == null || groupIds.isEmpty) {
      return [];
    }

    try {
      final groupsData = await _odooJsonRpc('call', {
        'service': 'object',
        'method': 'execute_kw',
        'args': [
          database,
          _userId,
          _password,
          'res.groups',
          'read',
          [groupIds],
          {
            'fields': ['name'],
          },
        ],
      });

      if (groupsData != null) {
        return groupsData.map<String>((group) => group['name'] ?? '').toList();
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  // جلب التذاكر
  Future<List<Ticket>> getTickets({String? searchQuery, int limit = 80}) async {
    if (_userId == null || _password == null) {
      throw Exception('يجب تسجيل الدخول أولاً');
    }

    try {
      // أولاً، تحقق من وجود وحدة helpdesk
      try {
        await _odooJsonRpc('call', {
          'service': 'object',
          'method': 'execute_kw',
          'args': [
            database,
            _userId,
            _password,
            'helpdesk.ticket',
            'check_access_rights',
            ['read'],
            {'raise_exception': false},
          ],
        });
      } catch (e) {
        print('Helpdesk module not available, trying project.task: $e');
        // إذا فشل، جرب استخدام project.task بدلاً من helpdesk.ticket
        return await _getProjectTasks(searchQuery: searchQuery, limit: limit);
      }

      // إعداد شروط البحث
      List<dynamic> searchDomain = [
        ['active', '=', true],
      ];

      // إضافة شرط البحث إذا تم توفيره
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        searchDomain.add('|');
        searchDomain.add('|');
        searchDomain.add('|');
        searchDomain.add('|');
        searchDomain.add('|');
        searchDomain.add([
          'name',
          'ilike',
          searchQuery,
        ]); // البحث في عنوان التذكرة
        searchDomain.add([
          'description',
          'ilike',
          searchQuery,
        ]); // البحث في الوصف
        searchDomain.add([
          'partner_id.name',
          'ilike',
          searchQuery,
        ]); // البحث في اسم العميل
        searchDomain.add([
          'partner_id.display_name',
          'ilike',
          searchQuery,
        ]); // البحث في الاسم المعروض للعميل
        searchDomain.add([
          'partner_id.phone',
          'ilike',
          searchQuery,
        ]); // البحث في رقم هاتف العميل
        searchDomain.add([
          'partner_id.mobile',
          'ilike',
          searchQuery,
        ]); // البحث في رقم الجوال للعميل
      }

      print('Attempting to fetch helpdesk tickets...');
      final ticketsData = await _odooJsonRpc('call', {
        'service': 'object',
        'method': 'execute_kw',
        'args': [
          database,
          _userId,
          _password,
          'helpdesk.ticket',
          'search_read',
          [searchDomain],
          {
            'fields': [
              'id',
              'name',
              'description',
              'stage_id',
              'priority',
              'partner_id',
              'partner_name',
              'partner_email',
              'partner_phone',
              'user_id',
              'create_date',
              'write_date',
              'active',
              'team_id',
              'company_id',
              'tag_ids',
              'kanban_state',
              'color',
              'date_last_stage_update',
              'close_date',
              'sla_deadline',
              'rating_last_value',
              'x_product_category',
              'x_partner_mobile',
              'x_partner_address',
              'x_partner_city',
              'x_state_id',
            ],
            'order': 'id asc', // ترتيب حسب ID (الأقدم أولاً)
            'limit': limit,
          },
        ],
      });

      print('Helpdesk tickets response: $ticketsData');

      if (ticketsData != null) {
        print('Converting ${ticketsData.length} tickets to Ticket objects...');
        final tickets = <Ticket>[];
        for (int i = 0; i < ticketsData.length; i++) {
          try {
            final ticketData = ticketsData[i];
            final ticket = Ticket(
              id: ticketData['id'],
              name: ticketData['name'] ?? 'تذكرة بدون عنوان',
              description: _extractStringValue(ticketData['description']),
              stage:
                  _extractStringValue(
                    ticketData['stage_id'] != null &&
                            ticketData['stage_id'] is List
                        ? ticketData['stage_id'][1]
                        : null,
                    defaultValue: 'غير محدد',
                  ) ??
                  'غير محدد',
              priority: ticketData['priority']?.toString() ?? '0',
              partnerId:
                  ticketData['partner_id'] != null &&
                          ticketData['partner_id'] is List
                      ? ticketData['partner_id'][0]
                      : null,
              partnerName:
                  ticketData['partner_id'] != null &&
                          ticketData['partner_id'] is List
                      ? ticketData['partner_id'][1]
                      : null,
              userId:
                  ticketData['user_id'] != null && ticketData['user_id'] is List
                      ? ticketData['user_id'][0]
                      : null,
              userName:
                  ticketData['user_id'] != null && ticketData['user_id'] is List
                      ? ticketData['user_id'][1]
                      : null,
              createDate: DateTime.parse(ticketData['create_date']),
              writeDate: DateTime.parse(ticketData['write_date']),
              teamId:
                  ticketData['team_id'] != null && ticketData['team_id'] is List
                      ? ticketData['team_id'][0]
                      : null,
              teamName: _extractStringValue(
                ticketData['team_id'] != null && ticketData['team_id'] is List
                    ? ticketData['team_id'][1]
                    : null,
              ),
              active: ticketData['active'] ?? true,
              email: _extractStringValue(ticketData['partner_email']),
              phone: _extractStringValue(ticketData['partner_phone']),
              // الحقول الجديدة من helpdesk.ticket
              mobile: _extractStringValue(ticketData['x_partner_mobile']),
              street: _extractStringValue(ticketData['x_partner_address']),
              street2: null, // غير متوفر في helpdesk.ticket
              city: _extractStringValue(
                ticketData['x_partner_city'] != null &&
                        ticketData['x_partner_city'] is List
                    ? ticketData['x_partner_city'][1]
                    : null,
              ),
              state: _extractStringValue(
                ticketData['x_state_id'] != null &&
                        ticketData['x_state_id'] is List
                    ? ticketData['x_state_id'][1]
                    : null,
              ),
              country: null, // غير متوفر في helpdesk.ticket
              zip: null, // غير متوفر في helpdesk.ticket
              companyName: _extractStringValue(
                ticketData['company_id'] != null &&
                        ticketData['company_id'] is List
                    ? ticketData['company_id'][1]
                    : null,
              ),
              category: _extractStringValue(
                ticketData['x_product_category'] != null &&
                        ticketData['x_product_category'] is List
                    ? ticketData['x_product_category'][1]
                    : null,
              ),
              tags:
                  ticketData['tag_ids'] is List &&
                          (ticketData['tag_ids'] as List).isNotEmpty
                      ? (ticketData['tag_ids'] as List)
                          .map((e) => e.toString())
                          .toList()
                      : null,
              kanbanState: _extractStringValue(ticketData['kanban_state']),
              color: _extractIntValue(ticketData['color']),
              dateDeadline: _extractDateValue(ticketData['sla_deadline']),
              dateLastStageUpdate: _extractDateValue(
                ticketData['date_last_stage_update'],
              ),
              closeDate: _extractDateValue(ticketData['close_date']),
              ratingLastValue: _extractDoubleValue(
                ticketData['rating_last_value'],
              ),
              slaDeadline: _extractDateValue(ticketData['sla_deadline']),
              slaReachedDatetime: null, // غير متوفر في helpdesk.ticket
            );

            tickets.add(ticket);
          } catch (e) {
            print('Error processing ticket at index $i: $e');
            print('Ticket data: ${ticketsData[i]}');
          }
        }
        print('Successfully converted ${tickets.length} tickets');
        return tickets;
      }

      return [];
    } catch (e) {
      throw Exception('فشل في جلب التذاكر: ${e.toString()}');
    }
  }

  // دالة مساعدة لاستخراج القيم النصية من Odoo
  String? _extractStringValue(dynamic value, {String? defaultValue}) {
    if (value == null || value == false) {
      return defaultValue;
    }
    if (value is String) {
      return value.isEmpty ? defaultValue : value;
    }
    return value.toString();
  }

  // دالة مساعدة لاستخراج التواريخ من Odoo
  DateTime? _extractDateValue(dynamic value) {
    if (value == null || value == false) {
      return null;
    }
    if (value is String) {
      return DateTime.tryParse(value);
    }
    return null;
  }

  // دالة مساعدة لاستخراج القيم الرقمية من Odoo
  double? _extractDoubleValue(dynamic value) {
    if (value == null || value == false) {
      return null;
    }
    if (value is num) {
      return value.toDouble();
    }
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  // دالة مساعدة لاستخراج القيم الصحيحة من Odoo
  int? _extractIntValue(dynamic value) {
    if (value == null || value == false) {
      return null;
    }
    if (value is int) {
      return value;
    }
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  // استكشاف حقول نموذج helpdesk.ticket
  Future<Map<String, dynamic>> exploreHelpdeskFields() async {
    try {
      final fieldsData = await _odooJsonRpc('call', {
        'service': 'object',
        'method': 'execute_kw',
        'args': [
          database,
          _userId,
          _password,
          'helpdesk.ticket',
          'fields_get',
          [],
          {},
        ],
      });

      print('=== Helpdesk Ticket Fields ===');
      if (fieldsData != null) {
        final fields = fieldsData as Map<String, dynamic>;
        fields.forEach((fieldName, fieldInfo) {
          final info = fieldInfo as Map<String, dynamic>;
          print('$fieldName: ${info['type']} - ${info['string']}');
        });
      }

      return fieldsData ?? {};
    } catch (e) {
      print('Error exploring helpdesk fields: $e');
      return {};
    }
  }

  // إثراء التذكرة بمعلومات العميل التفصيلية
  Future<Ticket> _enrichTicketWithPartnerData(Ticket ticket) async {
    try {
      if (ticket.partnerId == null) return ticket;

      final partnerData = await _odooJsonRpc('call', {
        'service': 'object',
        'method': 'execute_kw',
        'args': [
          database,
          _userId,
          _password,
          'res.partner',
          'read',
          [ticket.partnerId],
          {
            'fields': [
              'email',
              'phone',
              'mobile',
              'street',
              'street2',
              'city',
              'state_id',
              'country_id',
              'zip',
            ],
          },
        ],
      });

      if (partnerData != null && partnerData.isNotEmpty) {
        final partner = partnerData[0];
        return ticket.copyWith(
          email: _extractStringValue(partner['email']),
          phone: _extractStringValue(partner['phone']),
          mobile: _extractStringValue(partner['mobile']),
          street: _extractStringValue(partner['street']),
          street2: _extractStringValue(partner['street2']),
          city: _extractStringValue(partner['city']),
          state: _extractStringValue(
            partner['state_id'] != null && partner['state_id'] is List
                ? partner['state_id'][1]
                : null,
          ),
          country: _extractStringValue(
            partner['country_id'] != null && partner['country_id'] is List
                ? partner['country_id'][1]
                : null,
          ),
          zip: _extractStringValue(partner['zip']),
        );
      }
    } catch (e) {
      print('Error enriching ticket with partner data: $e');
    }
    return ticket;
  }

  // جلب المهام من project.task كبديل لـ helpdesk.ticket
  Future<List<Ticket>> _getProjectTasks({
    String? searchQuery,
    int limit = 80,
  }) async {
    try {
      // إعداد شروط البحث
      List<dynamic> searchDomain = [
        ['active', '=', true],
      ];

      // إضافة شرط البحث إذا تم توفيره
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        searchDomain.add('|');
        searchDomain.add('|');
        searchDomain.add('|');
        searchDomain.add('|');
        searchDomain.add('|');
        searchDomain.add([
          'name',
          'ilike',
          searchQuery,
        ]); // البحث في عنوان المهمة
        searchDomain.add([
          'description',
          'ilike',
          searchQuery,
        ]); // البحث في الوصف
        searchDomain.add([
          'partner_id.name',
          'ilike',
          searchQuery,
        ]); // البحث في اسم العميل
        searchDomain.add([
          'partner_id.display_name',
          'ilike',
          searchQuery,
        ]); // البحث في الاسم المعروض للعميل
        searchDomain.add([
          'partner_id.phone',
          'ilike',
          searchQuery,
        ]); // البحث في رقم هاتف العميل
        searchDomain.add([
          'partner_id.mobile',
          'ilike',
          searchQuery,
        ]); // البحث في رقم الجوال للعميل
      }

      print('Attempting to fetch project tasks as fallback...');
      final tasksData = await _odooJsonRpc('call', {
        'service': 'object',
        'method': 'execute_kw',
        'args': [
          database,
          _userId,
          _password,
          'project.task',
          'search_read',
          [searchDomain],
          {
            'fields': [
              'id',
              'name',
              'description',
              'stage_id',
              'priority',
              'partner_id',
              'user_id',
              'create_date',
              'write_date',
              'active',
            ],
            'order': 'id asc', // ترتيب حسب ID (الأقدم أولاً)
            'limit': limit,
          },
        ],
      });

      print('Project tasks response: $tasksData');

      if (tasksData != null) {
        return tasksData.map<Ticket>((taskData) {
          return Ticket(
            id: taskData['id'],
            name: taskData['name'] ?? 'مهمة بدون عنوان',
            description:
                taskData['description'] is String
                    ? taskData['description']
                    : null,
            stage:
                taskData['stage_id'] != null && taskData['stage_id'] is List
                    ? taskData['stage_id'][1]
                    : 'غير محدد',
            priority: taskData['priority']?.toString() ?? '0',
            partnerId:
                taskData['partner_id'] != null && taskData['partner_id'] is List
                    ? taskData['partner_id'][0]
                    : null,
            partnerName:
                taskData['partner_id'] != null && taskData['partner_id'] is List
                    ? taskData['partner_id'][1]
                    : null,
            userId:
                taskData['user_id'] != null && taskData['user_id'] is List
                    ? taskData['user_id'][0]
                    : null,
            userName:
                taskData['user_id'] != null && taskData['user_id'] is List
                    ? taskData['user_id'][1]
                    : null,
            createDate: DateTime.parse(taskData['create_date']),
            writeDate: DateTime.parse(taskData['write_date']),
            active: taskData['active'] ?? true,
          );
        }).toList();
      }

      return [];
    } catch (e) {
      throw Exception('فشل في جلب المهام: ${e.toString()}');
    }
  }

  // جلب رسائل التذكرة
  Future<List<TicketMessage>> getTicketMessages(int ticketId) async {
    if (_userId == null || _password == null) {
      throw Exception('يجب تسجيل الدخول أولاً');
    }

    try {
      final messagesData = await _odooJsonRpc('call', {
        'service': 'object',
        'method': 'execute_kw',
        'args': [
          database,
          _userId,
          _password,
          'mail.message',
          'search_read',
          [
            [
              ['res_id', '=', ticketId],
              ['res_model', '=', 'helpdesk.ticket'],
            ],
          ],
          {
            'fields': [
              'id',
              'subject',
              'body',
              'message_type',
              'author_id',
              'date',
              'res_id',
              'res_model',
              'attachment_ids',
            ],
            'order': 'date desc',
          },
        ],
      });

      if (messagesData != null) {
        return messagesData.map<TicketMessage>((messageData) {
          return TicketMessage(
            id: messageData['id'],
            subject: messageData['subject'] ?? '',
            body: messageData['body'] ?? '',
            messageType: messageData['message_type'] ?? 'comment',
            authorId:
                messageData['author_id'] != null &&
                        messageData['author_id'] is List
                    ? messageData['author_id'][0]
                    : null,
            authorName:
                messageData['author_id'] != null &&
                        messageData['author_id'] is List
                    ? messageData['author_id'][1]
                    : null,
            date: DateTime.parse(messageData['date']),
            resId: messageData['res_id'],
            resModel: messageData['res_model'],
            attachmentIds:
                messageData['attachment_ids'] != null
                    ? List<int>.from(messageData['attachment_ids'])
                    : [],
          );
        }).toList();
      }

      return [];
    } catch (e) {
      print('Error fetching ticket messages: $e');
      return [];
    }
  }

  // جلب مرفقات التذكرة
  Future<List<TicketAttachment>> getTicketAttachments(int ticketId) async {
    if (_userId == null || _password == null) {
      throw Exception('يجب تسجيل الدخول أولاً');
    }

    try {
      final attachmentsData = await _odooJsonRpc('call', {
        'service': 'object',
        'method': 'execute_kw',
        'args': [
          database,
          _userId,
          _password,
          'ir.attachment',
          'search_read',
          [
            [
              ['res_id', '=', ticketId],
              ['res_model', '=', 'helpdesk.ticket'],
            ],
          ],
          {
            'fields': [
              'id',
              'name',
              'mimetype',
              'file_size',
              'url',
              'res_id',
              'res_model',
              'create_date',
            ],
          },
        ],
      });

      if (attachmentsData != null) {
        return attachmentsData.map<TicketAttachment>((attachmentData) {
          return TicketAttachment(
            id: attachmentData['id'],
            name: attachmentData['name'] ?? 'ملف غير معروف',
            mimetype: attachmentData['mimetype'] ?? 'application/octet-stream',
            fileSize: attachmentData['file_size'] ?? 0,
            url: attachmentData['url'],
            resId: attachmentData['res_id'],
            resModel: attachmentData['res_model'],
            createDate: DateTime.parse(attachmentData['create_date']),
          );
        }).toList();
      }

      return [];
    } catch (e) {
      print('Error fetching ticket attachments: $e');
      return [];
    }
  }

  // تحميل المرفق
  Future<String?> downloadAttachment(int attachmentId) async {
    try {
      // إنشاء رابط تحميل المرفق
      final downloadUrl =
          'https://corsproxy.io/?${Uri.encodeComponent('$baseUrl/web/content/$attachmentId')}';

      return downloadUrl;
    } catch (e) {
      print('Error generating download URL: $e');
      return null;
    }
  }

  // تحديث تذكرة
  Future<bool> updateTicket(int ticketId, Map<String, dynamic> values) async {
    if (_userId == null || _password == null) {
      throw Exception('يجب تسجيل الدخول أولاً');
    }

    try {
      final result = await _odooJsonRpc('call', {
        'service': 'object',
        'method': 'execute_kw',
        'args': [
          database,
          _userId,
          _password,
          'helpdesk.ticket',
          'write',
          [ticketId, values],
        ],
      });

      return result == true;
    } catch (e) {
      throw Exception('فشل في تحديث التذكرة: ${e.toString()}');
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    _userId = null;
    _password = null;
    _email = null;
  }
}
