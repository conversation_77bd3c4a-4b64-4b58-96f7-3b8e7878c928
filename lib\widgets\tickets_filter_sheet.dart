import 'package:flutter/material.dart';

class TicketsFilterSheet extends StatefulWidget {
  final String? selectedStage;
  final String? selectedPriority;
  final Function(String?, String?) onApplyFilter;

  const TicketsFilterSheet({
    super.key,
    this.selectedStage,
    this.selectedPriority,
    required this.onApplyFilter,
  });

  @override
  State<TicketsFilterSheet> createState() => _TicketsFilterSheetState();
}

class _TicketsFilterSheetState extends State<TicketsFilterSheet> {
  String? _selectedStage;
  String? _selectedPriority;

  final Map<String, String> _stages = {
    'new': 'جديد',
    'in_progress': 'قيد التنفيذ',
    'solved': 'تم الحل',
    'cancelled': 'ملغي',
  };

  final Map<String, String> _priorities = {
    '0': 'منخفضة',
    '1': 'عادية',
    '2': 'عالية',
    '3': 'عاجلة',
  };

  @override
  void initState() {
    super.initState();
    _selectedStage = widget.selectedStage;
    _selectedPriority = widget.selectedPriority;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس الورقة
          Row(
            children: [
              Text(
                'فلترة التذاكر',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // فلتر الحالة
          Text(
            'الحالة',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // خيار "الكل"
              FilterChip(
                label: const Text('الكل'),
                selected: _selectedStage == null,
                onSelected: (selected) {
                  setState(() {
                    _selectedStage = selected ? null : _selectedStage;
                  });
                },
              ),
              // خيارات الحالات
              ..._stages.entries.map((entry) => FilterChip(
                label: Text(entry.value),
                selected: _selectedStage == entry.key,
                onSelected: (selected) {
                  setState(() {
                    _selectedStage = selected ? entry.key : null;
                  });
                },
              )),
            ],
          ),
          const SizedBox(height: 24),

          // فلتر الأولوية
          Text(
            'الأولوية',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // خيار "الكل"
              FilterChip(
                label: const Text('الكل'),
                selected: _selectedPriority == null,
                onSelected: (selected) {
                  setState(() {
                    _selectedPriority = selected ? null : _selectedPriority;
                  });
                },
              ),
              // خيارات الأولويات
              ..._priorities.entries.map((entry) => FilterChip(
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _getPriorityIcon(entry.key),
                    const SizedBox(width: 4),
                    Text(entry.value),
                  ],
                ),
                selected: _selectedPriority == entry.key,
                onSelected: (selected) {
                  setState(() {
                    _selectedPriority = selected ? entry.key : null;
                  });
                },
              )),
            ],
          ),
          const SizedBox(height: 32),

          // أزرار العمل
          Row(
            children: [
              // زر مسح الفلاتر
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _selectedStage = null;
                      _selectedPriority = null;
                    });
                  },
                  child: const Text('مسح الفلاتر'),
                ),
              ),
              const SizedBox(width: 16),
              // زر تطبيق
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    widget.onApplyFilter(_selectedStage, _selectedPriority);
                    Navigator.of(context).pop();
                  },
                  child: const Text('تطبيق'),
                ),
              ),
            ],
          ),
          
          // إضافة مساحة للوحة المفاتيح
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }

  Widget _getPriorityIcon(String priority) {
    Color color;
    IconData icon;

    switch (priority) {
      case '3': // عاجل
        color = Colors.red;
        icon = Icons.priority_high;
        break;
      case '2': // عالي
        color = Colors.orange;
        icon = Icons.keyboard_arrow_up;
        break;
      case '1': // عادي
        color = Colors.blue;
        icon = Icons.remove;
        break;
      case '0': // منخفض
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.grey;
        icon = Icons.help_outline;
    }

    return Icon(
      icon,
      size: 16,
      color: color,
    );
  }
}
