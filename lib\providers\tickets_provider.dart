import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ticket.dart';
import '../services/unified_odoo_service.dart';
import '../services/local_storage_service.dart';
import '../services/sync_service.dart';
import 'auth_provider.dart';
import 'connectivity_provider.dart';

// حالة التذاكر
enum TicketsState { initial, loading, loaded, error, syncing }

// فئة حالة التذاكر
class TicketsStateData {
  final TicketsState state;
  final List<Ticket> tickets;
  final String? errorMessage;
  final DateTime? lastSyncTime;

  const TicketsStateData({
    required this.state,
    this.tickets = const [],
    this.errorMessage,
    this.lastSyncTime,
  });

  TicketsStateData copyWith({
    TicketsState? state,
    List<Ticket>? tickets,
    String? errorMessage,
    DateTime? lastSyncTime,
  }) {
    return TicketsStateData(
      state: state ?? this.state,
      tickets: tickets ?? this.tickets,
      errorMessage: errorMessage ?? this.errorMessage,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
    );
  }
}

// مزود التذاكر
class TicketsNotifier extends StateNotifier<TicketsStateData> {
  final UnifiedOdooService _odooService;
  final Ref _ref;

  TicketsNotifier(this._odooService, this._ref)
    : super(const TicketsStateData(state: TicketsState.initial)) {
    _loadTickets();
  }

  // تحميل التذاكر
  Future<void> _loadTickets() async {
    // تحميل التذاكر المحفوظة محلياً أولاً
    final localTickets = LocalStorageService.getTickets();
    final lastSync = await LocalStorageService.getLastSyncTime();

    if (localTickets.isNotEmpty) {
      state = TicketsStateData(
        state: TicketsState.loaded,
        tickets: localTickets,
        lastSyncTime: lastSync,
      );
    }

    // محاولة تحديث التذاكر من الخادم
    await refreshTickets();
  }

  // تحديث التذاكر من الخادم
  Future<void> refreshTickets() async {
    final isConnected = _ref.read(connectivityProvider);
    final currentUser = _ref.read(currentUserProvider);

    if (!isConnected || currentUser == null) {
      // إذا لم يكن هناك اتصال أو مستخدم، نعرض البيانات المحلية
      return;
    }

    state = state.copyWith(state: TicketsState.syncing);

    try {
      print('TicketsProvider: Starting to fetch tickets...');
      final tickets = await _odooService.getTickets();
      print('TicketsProvider: Received ${tickets.length} tickets from service');

      // حفظ التذاكر محلياً
      await LocalStorageService.saveTickets(tickets);
      await LocalStorageService.setLastSyncTime(DateTime.now());
      print('TicketsProvider: Saved tickets to local storage');

      state = TicketsStateData(
        state: TicketsState.loaded,
        tickets: tickets,
        lastSyncTime: DateTime.now(),
      );
      print('TicketsProvider: Updated state with ${tickets.length} tickets');
    } catch (e) {
      print('TicketsProvider: Error fetching tickets: $e');
      // في حالة الخطأ، نحتفظ بالبيانات المحلية
      final localTickets = LocalStorageService.getTickets();
      print(
        'TicketsProvider: Using ${localTickets.length} local tickets as fallback',
      );
      state = TicketsStateData(
        state: TicketsState.error,
        tickets: localTickets,
        errorMessage: 'خطأ في تحديث التذاكر: ${e.toString()}',
        lastSyncTime: await LocalStorageService.getLastSyncTime(),
      );
    }
  }

  // البحث المباشر في التذاكر
  Future<void> searchTickets(String searchQuery) async {
    final isConnected = _ref.read(connectivityProvider);
    final currentUser = _ref.read(currentUserProvider);

    if (!isConnected || currentUser == null) {
      // البحث المحلي إذا لم يكن هناك اتصال
      _searchLocally(searchQuery);
      return;
    }

    if (searchQuery.trim().isEmpty) {
      // إذا كان البحث فارغاً، أعد تحميل التذاكر العادية
      await refreshTickets();
      return;
    }

    state = state.copyWith(state: TicketsState.loading);

    try {
      print('TicketsProvider: Searching for: $searchQuery');
      final tickets = await _odooService.getTickets(
        searchQuery: searchQuery,
        limit: 200, // حد أعلى للبحث
      );
      print('TicketsProvider: Found ${tickets.length} tickets matching search');

      state = TicketsStateData(
        state: TicketsState.loaded,
        tickets: tickets,
        lastSyncTime: DateTime.now(),
      );
    } catch (e) {
      print('TicketsProvider: Error searching tickets: $e');
      // في حالة الخطأ، جرب البحث المحلي
      _searchLocally(searchQuery);
    }
  }

  // البحث المحلي في التذاكر المحفوظة
  void _searchLocally(String searchQuery) {
    final localTickets = LocalStorageService.getTickets();

    if (searchQuery.trim().isEmpty) {
      state = TicketsStateData(
        state: TicketsState.loaded,
        tickets: localTickets,
        lastSyncTime: state.lastSyncTime,
      );
      return;
    }

    final query = searchQuery.toLowerCase();
    final filteredTickets =
        localTickets.where((ticket) {
          return ticket.name.toLowerCase().contains(query) ||
              (ticket.cleanDescription?.toLowerCase().contains(query) ?? false);
        }).toList();

    // ترتيب حسب ID (الأقدم أولاً)
    filteredTickets.sort((a, b) => a.id.compareTo(b.id));

    state = TicketsStateData(
      state: TicketsState.loaded,
      tickets: filteredTickets,
      lastSyncTime: state.lastSyncTime,
    );
  }

  // تحديث تذكرة
  Future<bool> updateTicket(int ticketId, Map<String, dynamic> values) async {
    final isConnected = _ref.read(connectivityProvider);

    // تحديث التذكرة محلياً أولاً
    final updatedTickets =
        state.tickets.map((ticket) {
          if (ticket.id == ticketId) {
            // تطبيق التحديثات على التذكرة
            return ticket.copyWith(
              stage: values['stage_id']?.toString() ?? ticket.stage,
              priority: values['priority']?.toString() ?? ticket.priority,
              description: values['description'] ?? ticket.description,
              writeDate: DateTime.now(),
            );
          }
          return ticket;
        }).toList();

    state = state.copyWith(tickets: updatedTickets);
    await LocalStorageService.saveTickets(updatedTickets);

    if (!isConnected) {
      // في وضع عدم الاتصال، نضيف العملية للقائمة المؤجلة
      await SyncService().addPendingTicketUpdate(ticketId, values);
      return true; // نعتبر العملية ناجحة محلياً
    }

    try {
      final success = await _odooService.updateTicket(ticketId, values);

      if (!success) {
        // إذا فشل التحديث على الخادم، نضيف العملية للقائمة المؤجلة
        await SyncService().addPendingTicketUpdate(ticketId, values);
      }

      return true; // نعتبر العملية ناجحة لأنها محفوظة محلياً
    } catch (e) {
      // في حالة الخطأ، نضيف العملية للقائمة المؤجلة
      await SyncService().addPendingTicketUpdate(ticketId, values);
      return true; // نعتبر العملية ناجحة لأنها محفوظة محلياً
    }
  }

  // البحث المحلي في التذاكر المحملة حالياً (للاستخدام في الواجهة)
  List<Ticket> filterTicketsLocally(String query) {
    if (query.isEmpty) return state.tickets;

    return state.tickets.where((ticket) {
      return ticket.name.toLowerCase().contains(query.toLowerCase()) ||
          (ticket.cleanDescription?.toLowerCase().contains(
                query.toLowerCase(),
              ) ??
              false) ||
          ticket.partnerName?.toLowerCase().contains(query.toLowerCase()) ==
              true;
    }).toList();
  }

  // فلترة التذاكر حسب الحالة
  List<Ticket> getTicketsByStage(String stage) {
    return state.tickets.where((ticket) => ticket.stage == stage).toList();
  }

  // فلترة التذاكر حسب الأولوية
  List<Ticket> getTicketsByPriority(String priority) {
    return state.tickets
        .where((ticket) => ticket.priority == priority)
        .toList();
  }

  // مسح رسالة الخطأ
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  // إعادة تحميل التذاكر
  Future<void> reload() async {
    state = state.copyWith(state: TicketsState.loading);
    await _loadTickets();
  }
}

// مزود التذاكر
final ticketsProvider =
    StateNotifierProvider<TicketsNotifier, TicketsStateData>((ref) {
      final odooService = ref.watch(odooServiceProvider);
      return TicketsNotifier(odooService, ref);
    });

// مزودات مساعدة
final ticketsListProvider = Provider<List<Ticket>>((ref) {
  final ticketsState = ref.watch(ticketsProvider);
  return ticketsState.tickets;
});

final ticketsLoadingProvider = Provider<bool>((ref) {
  final ticketsState = ref.watch(ticketsProvider);
  return ticketsState.state == TicketsState.loading ||
      ticketsState.state == TicketsState.syncing;
});

final ticketsErrorProvider = Provider<String?>((ref) {
  final ticketsState = ref.watch(ticketsProvider);
  return ticketsState.errorMessage;
});

final lastSyncTimeProvider = Provider<DateTime?>((ref) {
  final ticketsState = ref.watch(ticketsProvider);
  return ticketsState.lastSyncTime;
});

// مزود للحصول على تذكرة محددة
final ticketProvider = Provider.family<Ticket?, int>((ref, ticketId) {
  final tickets = ref.watch(ticketsListProvider);
  try {
    return tickets.firstWhere((ticket) => ticket.id == ticketId);
  } catch (e) {
    return null;
  }
});

// مزود إحصائيات التذاكر
final ticketsStatsProvider = Provider<Map<String, int>>((ref) {
  final tickets = ref.watch(ticketsListProvider);

  final stats = <String, int>{
    'total': tickets.length,
    'new': 0,
    'in_progress': 0,
    'solved': 0,
    'cancelled': 0,
    'high_priority': 0,
    'urgent_priority': 0,
  };

  for (final ticket in tickets) {
    switch (ticket.stage) {
      case 'new':
        stats['new'] = (stats['new'] ?? 0) + 1;
        break;
      case 'in_progress':
        stats['in_progress'] = (stats['in_progress'] ?? 0) + 1;
        break;
      case 'solved':
        stats['solved'] = (stats['solved'] ?? 0) + 1;
        break;
      case 'cancelled':
        stats['cancelled'] = (stats['cancelled'] ?? 0) + 1;
        break;
    }

    if (ticket.priority == '2') {
      stats['high_priority'] = (stats['high_priority'] ?? 0) + 1;
    } else if (ticket.priority == '3') {
      stats['urgent_priority'] = (stats['urgent_priority'] ?? 0) + 1;
    }
  }

  return stats;
});
