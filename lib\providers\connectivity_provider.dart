import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

// مزود حالة الاتصال
class ConnectivityNotifier extends StateNotifier<bool> {
  late StreamSubscription<ConnectivityResult> _subscription;
  final Connectivity _connectivity = Connectivity();

  ConnectivityNotifier() : super(true) {
    _checkInitialConnectivity();
    _subscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectivity,
    );
  }

  Future<void> _checkInitialConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectivity(result);
    } catch (e) {
      // في حالة الخطأ، نفترض عدم وجود اتصال
      state = false;
    }
  }

  void _updateConnectivity(ConnectivityResult result) {
    // إذا كان هناك أي نوع من الاتصال (WiFi, Mobile, Ethernet)
    final hasConnection =
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.ethernet;

    state = hasConnection;
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}

// مزود الاتصال
final connectivityProvider = StateNotifierProvider<ConnectivityNotifier, bool>((
  ref,
) {
  return ConnectivityNotifier();
});

// مزود لمراقبة تغييرات الاتصال
final connectivityStreamProvider = StreamProvider<bool>((ref) {
  final connectivity = Connectivity();

  return connectivity.onConnectivityChanged.map((result) {
    return result == ConnectivityResult.wifi ||
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.ethernet;
  });
});

// مزود لحالة الاتصال مع تفاصيل إضافية
class ConnectivityState {
  final bool isConnected;
  final ConnectivityResult connectionType;
  final DateTime lastChecked;

  const ConnectivityState({
    required this.isConnected,
    required this.connectionType,
    required this.lastChecked,
  });

  ConnectivityState copyWith({
    bool? isConnected,
    ConnectivityResult? connectionType,
    DateTime? lastChecked,
  }) {
    return ConnectivityState(
      isConnected: isConnected ?? this.isConnected,
      connectionType: connectionType ?? this.connectionType,
      lastChecked: lastChecked ?? this.lastChecked,
    );
  }

  String get connectionTypeText {
    switch (connectionType) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'بيانات الجوال';
      case ConnectivityResult.ethernet:
        return 'إيثرنت';
      case ConnectivityResult.none:
        return 'غير متصل';
      default:
        return 'غير معروف';
    }
  }
}

class DetailedConnectivityNotifier extends StateNotifier<ConnectivityState> {
  late StreamSubscription<ConnectivityResult> _subscription;
  final Connectivity _connectivity = Connectivity();

  DetailedConnectivityNotifier()
    : super(
        ConnectivityState(
          isConnected: false,
          connectionType: ConnectivityResult.none,
          lastChecked: DateTime.now(),
        ),
      ) {
    _checkInitialConnectivity();
    _subscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectivity,
    );
  }

  Future<void> _checkInitialConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectivity(result);
    } catch (e) {
      state = state.copyWith(
        isConnected: false,
        connectionType: ConnectivityResult.none,
        lastChecked: DateTime.now(),
      );
    }
  }

  void _updateConnectivity(ConnectivityResult result) {
    final hasConnection =
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.ethernet;

    state = ConnectivityState(
      isConnected: hasConnection,
      connectionType: result,
      lastChecked: DateTime.now(),
    );
  }

  // فحص الاتصال يدوياً
  Future<void> checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectivity(result);
    } catch (e) {
      state = state.copyWith(
        isConnected: false,
        connectionType: ConnectivityResult.none,
        lastChecked: DateTime.now(),
      );
    }
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}

// مزود الاتصال المفصل
final detailedConnectivityProvider =
    StateNotifierProvider<DetailedConnectivityNotifier, ConnectivityState>((
      ref,
    ) {
      return DetailedConnectivityNotifier();
    });

// مزودات مساعدة
final isOnlineProvider = Provider<bool>((ref) {
  return ref.watch(connectivityProvider);
});

final isOfflineProvider = Provider<bool>((ref) {
  return !ref.watch(connectivityProvider);
});

final connectionTypeProvider = Provider<ConnectivityResult>((ref) {
  final connectivityState = ref.watch(detailedConnectivityProvider);
  return connectivityState.connectionType;
});

final connectionTypeTextProvider = Provider<String>((ref) {
  final connectivityState = ref.watch(detailedConnectivityProvider);
  return connectivityState.connectionTypeText;
});
